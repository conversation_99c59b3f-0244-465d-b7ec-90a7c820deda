<GeminiOptimizationFrom>Docker/基础命令.md</GeminiOptimizationFrom>
# Docker 基础命令

---

## 常见命令

```shell
# 登录 DockerHub
docker login
# 查看所有镜像
docker images
# 查看运行中的容器
docker ps
# 查看所有容器(包括停止的容器)
docker ps -a
# 删除镜像
docker rmi <image>
# 删除容器
docker rm <container>
# 停止容器
docker stop <container>
# 上传镜像(需要登录)
docker push <image>
# 查看 Docker 对象的元数据
# https://docs.docker.com/engine/reference/commandline/inspect/
docker inspect --format='{{json .Config}}' $INSTANCE_ID
# 使用Dockerfile制作镜像
docker build -t <镜像名称> <上下文目录>
```









## 基础模板

**Dockerfile**

```dockerfile
FROM python:3.9.16

WORKDIR /workspace
COPY sources.list /etc/apt/sources.list

# 忽略 apt 安装中的交互
ENV PIP_ROOT_USER_ACTION=ignore

RUN    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple ddddocr==1.4.7 flask==2.1.0 flask-cors==3.0.10 \
    && echo "Docker Image ubuntu:16.04-build-env Build Successfully!"

# 默认的执行命令
CMD ["python", "main.py"]

```



**docker-compose.yaml**

`docker-compose up`

`-d`: 后台运行

```yaml
version: '3.7'
services:
  <service-name>:
    container_name: <container-name>
    # 会自动重启
    restart: unless-stopped
    image: ubuntu:20.04
    build:
      context: ./context
      dockerfile: ./temp.Dockerfile
    # command: ["python", "/workspace/main.py"]
    command: >
      sh -c "./configure --enable-optimizations &&
             make &&
             make install"
    ports:
      - <host_port>:<container_port>
    volumes:
      # - ./back-end-support/main.py:/workspace/main.py:ro
      - <host_path>:<container_path>:ro
    working_dir: /workspace
    environment:
      - AUTHOR=Maikebuke
    cpus: 1
```





### 以非 root 用户启动 docker 容器

```shell
docker run -d \
    --name aria2-pro \
    --restart unless-stopped \
    --log-opt max-size=1m \
    -e PUID=$UID \
    -e PGID=$GID \
    -e UMASK_SET=022 \
    -e RPC_SECRET=<TOKEN> \
    -e RPC_PORT=6800 \
    -p 6800:6800 \
    -e LISTEN_PORT=6888 \
    -p 6888:6888 \
    -p 6888:6888/udp \
    -v $PWD/aria2-config:/config \
    -v $PWD/aria2-downloads:/downloads \
    p3terx/aria2-pro
```



| Parameter                     | Function                                                     |
| ----------------------------- | ------------------------------------------------------------ |
| `-e PUID=$UID` `-e PGID=$GID` | Bind UID and GID to the container, which means you can use a non-root user to manage downloaded files. |

