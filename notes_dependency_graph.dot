digraph KnowledgeMap {
    // 全局图、节点和边的样式设置
    graph [
        rankdir="LR",
        bgcolor="transparent",
        fontname="SimHei",
        fontsize=12,
        label="个人知识库依赖关系图 (增强版)"
    ];
    node [
        shape="box",
        style="rounded,filled",
        fillcolor="#F5F5F5",
        fontname="SimHei",
        color="#BDBDBD"
    ];
    edge [
        color="#616161",
        arrowhead="normal"
    ];

    // --- 00 底层系统与原理 ---
    subgraph cluster_00_System {
        label = "底层系统与原理";
        style = "rounded,dashed";
        color = "grey";

        node [fillcolor="#E3F2FD"];
        "编译工具链"; "ELF_文件格式"; "Makefile_指南"; "GCC_命令参考";
        "JMP_指令详解"; "内核内存初始化"; "进程调度机制"; "Clang_命令参考";
    }

    // --- 10 软件安全 ---
    subgraph cluster_10_Security {
        label = "软件安全";
        style = "rounded,dashed";
        color = "grey";

        subgraph cluster_11_Pwn {
            label = "二进制利用 (Pwn)";
            node [fillcolor="#FFEBEE"];
            "Libc_Malloc_内部机制" -> "Ptmalloc_Bins" [label="包含"];
            "Fastbin_Attack" -> "Ptmalloc_Bins" [label="基于"];
            "堆漏洞利用总结" -> "Fastbin_Attack";
            "RCTF2017_RNote2" -> "Fastbin_Attack" [label="实例"];
            "Pwn挑战部署" -> "Patchelf依赖导出";
            "Libc版本与Docker" -> "Pwn挑战部署";
        }

        subgraph cluster_12_Fuzzing {
            label = "模糊测试";
            node [fillcolor="#E8F5E9"];
            "AFL_使用指南" -> "AFL_文件格式";
            "AFL_开发笔记" -> "AFL_使用指南";
        }

        subgraph cluster_13_DynamicAnalysis {
            label = "动态分析";
            node [fillcolor="#FFF3E0"];
            "Intel_Pin"; "PANDA_RE"; "LibDFT"; "Strace";
        }
        
        subgraph cluster_14_ReverseEngineering {
            label = "逆向工程";
            node [fillcolor="#F3E5F5"];
            "IDA_Python脚本"; "IDA_基地址修改"; "Capstone";
        }
        
        subgraph cluster_15_Sanitizers {
            label = "Sanitizers";
            node [fillcolor="#EFEBE9"];
            "ThreadSanitizer" -> "竞争条件与数据竞争" [label="检测"];
        }
    }

    // --- 20 开发与工具 ---
    subgraph cluster_20_DevTools {
        label = "开发与工具";
        style = "rounded,dashed";
        color = "grey";

        subgraph cluster_21_LLVM {
            label = "LLVM";
            node [fillcolor="#E0F7FA"];
            "LLVM_概述" -> "Clang_组件";
            "LLVM_Pass入门" -> "LLVM_概述";
            "Pass静态插桩" -> "LLVM_Pass入门";
            "Pass获取CFG" -> "LLVM_Pass入门";
            "CMake与Clang命令" -> "Clang_组件";
            "Clang_组件" -> "Clang_命令参考";
        }

        subgraph cluster_22_QEMU {
            label = "QEMU";
            node [fillcolor="#F1F8E9"];
            "QEMU_整体架构"; "QEMU_插件机制";
            "WAJI_QEMU源码修改" -> "QEMU_插件机制" [label="依赖"];
            "PANDA_RE" -> "QEMU_整体架构" [label="基于"];
        }

        subgraph cluster_23_Python {
            label = "Python";
            node [fillcolor="#FFFDE7"];
            "Python_Ctypes"; "Python_Inspect"; "Python_Bytecode"; "Python_Selenium";
        }
    }

    // --- 30 研究与项目 ---
    subgraph cluster_30_Research {
        label = "研究与项目";
        style = "rounded,dashed";
        color = "grey";
        node [fillcolor="#EDE7F6"];
        "WAJI_项目大纲"; "自定义堆管理器"; "网络服务模型"; "Nuki_项目笔记"; "学术术语表";
    }

    // --- 跨领域依赖关系 (增强) ---
    // 安全工具与基础
    "Fastbin_Attack" -> "JMP_指令详解" [style=dotted, label="可能参考"];
    "LibDFT" -> "Intel_Pin" [label="基于"];
    "IDA_Python脚本" -> "Python_Inspect" [style=dotted, label="用于调试"];
    "Capstone" -> "JMP_指令详解" [style=dotted, label="解析"];
    
    // 项目与技术
    "WAJI_项目大纲" -> "QEMU_插件机制" [label="核心技术"];
    "WAJI_项目大纲" -> "自定义堆管理器" [label="研究对象"];
    "Nuki_项目笔记" -> "网络服务模型" [label="分析对象"];
    "Nuki_项目笔记" -> "LibDFT" [label="使用"];

    // 开发工具与底层原理
    "LLVM_Pass入门" -> "编译工具链" [style=dotted];
    "QEMU_整体架构" -> "编译工具链" [style=dotted];
    "Makefile_指南" -> "编译工具链" [label="管理"];
    "GCC_命令参考" -> "编译工具链" [label="实现"];
    
    // 安全与开发
    "AFL_使用指南" -> "GCC_命令参考" [label="依赖插桩"];
    "堆漏洞利用总结" -> "Libc_Malloc_内部机制";
    "Pwn挑战部署" -> "Tmux" [style=dotted, label="推荐使用"];
}
