<GeminiOptimizationFrom>LLVM/Clang/02. <PERSON><PERSON><PERSON> and Clang Commands.md</GeminiOptimizationFrom>
# CMake & Clang 的特殊命令

---



## Clang



### 常用选项

| 选项                 | 功能                       | 示例 |
| -------------------- | -------------------------- | ---- |
| -fsyntax-only        | 只进行语法检测而不输出文件 |      |
| -cc1 -x c <source.c> | 在使用 -cc1 选项时编译文件 |      |
| -Wall                | 开启所有的 Warning         |      |
| -Wextra              | 开启额外的 Waining         |      |
|                      |                            |      |



### Clang Plugin 的正确使用方式

> Clang 官网中对在 Clang Command Line 中使用插件的说明
>
> https://clang.llvm.org/docs/ClangPlugins.html#using-the-clang-command-line

简单而言，Clang 插件的 Action 加载方式称为 **Action Type**, 具体由 `ASTFrontendAction::getActionType()`  方法的返回值决定, 具体而言, 其返回一个类型为 `enum ActionType` 的枚举值, 其各个值得含义如下:

| enum ActionType         | Describe                                                     |
| ----------------------- | ------------------------------------------------------------ |
| CmdlineBeforeMainAction | 如果通过 Clang Command Line **显式**传递了当前插件的`action`名称， 才会在**Main Action** 执行**前**调用 |
| CmdlineAfterMainAction  | 如果通过 Clang Command Line **显式**传递了当前插件的`action`名称， 才会在 **Main Action** 执行**后**调用 |
| ReplaceAction           | 将 Clang Frontend Main Action 替换为当前插件的 Action        |
| AddBeforeMainAction     | 在 Main Action 前, 自动加载插件的 Action                     |
| AddAfterMainAction      | 在 Main Action 后, 自动加载插件的 Action                     |

具体而言, 修改 Clang Plugin 的 Action Type 方法如下:

```cpp
// Automatically run the plugin after the main AST action
PluginASTAction::ActionType getActionType() override {
  return AddAfterMainAction;
}
```



**加载插件**

```plain
$ clang -cc1 -help | grep plugin
  -add-plugin <name>      Use the named plugin action in addition to the default action
  -load <dsopath>         Load the named plugin (dynamic shared object)
  -plugin-arg-<name> <arg>
                          Pass <arg> to plugin <name>
  -plugin <name>          Use the named plugin action instead of the default action (use "help" to list available options)
```

简而言之:

	- 通过 `-load` 加载指定名称的插件动态库
	- 通过 `-plugin` 将 Frontend Action 替换为指定名称的 Action
	- 通过 `-add-plugin`  将指定的 Action 串联到 Default Action 链中



插件的加载方式有两种, 对于一个名为 `libHelloWorld.so` 的插件而言, 假设其注册方式如下:

```cpp
static FrontendPluginRegistry::Add< 具体实现插件的Action的类名 > X(
    /*Name=*/ "aaa",
    /*Description=*/ "Hello World Plugin"
);
```

其中, `FrontendPluginRegistry::Add`的第一个参数`Name` 为插件的 `Action Name`. 

- 如果 Action Type 为 `Cmdline*`, 则需要通过 `-plugin <Action Name>` 或 `-add-plugin <Action Name>` 的方式手动手动指定插件名称:

    其中, `-plugin`  的加载方式会使用指定的 Action 替换掉 Clang 默认的 Action, 此时会导致原有的"编译"过程终止, 无法得到任何的输出文件:

    ```shell
    $ clang -fplugin=build/libHelloWorld.so -Xclang -plugin -Xclang aaa -c ../test/HelloWorld-basic.cpp
    # OR
    $ clang -cc1 -load build/libHelloWorld.so -plugin aaa ../test/HelloWorld-basic.cpp 
    
    # outputs:
    (clang-tutor)  file: ../test/HelloWorld-basic.cpp
    (clang-tutor)  count: 3
    ```

    可以通过`-add-plugin` 的方式串联 Action, 此时会根据 `getActionType()` 指定的顺序进行插件加载, 参考 [加载顺序测试用例](https://github.com/llvm/llvm-project/blob/main/clang/test/Frontend/plugins-order.c). 

- 如果 Action Type 为 `add*MainAction`, 则插件会自动加载
    - 此时, 如果使用 `-plugin` 指定 Action 名称, 编译流程会终止, 包括指定的 Action 也不会执行;
    - 此时, `-add-plugin` 选项能够继续串联指定的 Action;



### 编译时显示所有隐藏的编译命令(但不运行)

> $ clang -help | grep "###"
>
> -###                    Print (but do not run) the commands to run for this compilation

例如, 可以使用如下命令查看 `Clang Plugin` 的加载情况:

```shell
$ clang++ -### -fplugin=libCodeStyleChecker.so -c HelloWorld-basic.cpp
```

得到以下输出:

```plain
clang version 12.0.1 (https://gitee.com/mirrors/LLVM.git fed41342a82f5a3a9201819a82bf7a48313e296b)
Target: x86_64-unknown-linux-gnu
Thread model: posix
InstalledDir: /usr/local/llvm/bin
 (in-process)
 "/usr/local/llvm/versions/llvmorg-12.0.1/bin/clang-12" "-cc1" "-triple" "x86_64-unknown-linux-gnu" "-emit-obj" "-mrelax-all" "--mrelax-relocations" "-disable-free" "-disable-llvm-verifier" "-discard-value-names" "-main-file-name" "HelloWorld-basic.cpp" "-mrelocation-model" "static" "-mframe-pointer=all" "-fmath-errno" "-fno-rounding-math" "-mconstructor-aliases" "-munwind-tables" "-target-cpu" "x86-64" "-tune-cpu" "generic" "-fno-split-dwarf-inlining" "-debugger-tuning=gdb" "-resource-dir" "/usr/local/llvm/versions/llvmorg-12.0.1/lib/clang/12.0.1" "-internal-isystem" "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../include/c++/11" "-internal-isystem" "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../include/x86_64-linux-gnu/c++/11" "-internal-isystem" "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../include/x86_64-linux-gnu/c++/11" "-internal-isystem" "/usr/lib/gcc/x86_64-linux-gnu/11/../../../../include/c++/11/backward" "-internal-isystem" "/usr/local/include" "-internal-isystem" "/usr/local/llvm/versions/llvmorg-12.0.1/lib/clang/12.0.1/include" "-internal-externc-isystem" "/usr/include/x86_64-linux-gnu" "-internal-externc-isystem" "/include" "-internal-externc-isystem" "/usr/include" "-fdeprecated-macro" "-fdebug-compilation-dir" "/home/<USER>/Workspace/Testspace/clang-ast/clang-tutor/test" "-ferror-limit" "19" "-fgnuc-version=4.2.1" "-fcxx-exceptions" "-fexceptions" "-fcolor-diagnostics" "-load" "../build/lib/libCodeStyleChecker.so" "-faddrsig" "-o" "HelloWorld-basic.o" "-x" "c++" "HelloWorld-basic.cpp"
```



### 使用 -verify 验证 clang 是否正确的捕获错误

`-verify` 选项默认验证以 `expected` 开头的注释指令, 如 `// expected-warning {{unused variable 'a'}}`

也可以使用 ` -verify=<prefixes>` 选项验证指定开头的注释指令, 如 `// lzh-warning {{unused variable 'a'}}`

>   -verify=<prefixes>      Verify diagnostic output using comment directives that start with prefixes in the comma-separated sequence <prefixes>
>   -verify                 Equivalent to -verify=expected

具体而言, 给定 C 语言代码以及命令, 可以实现验证:

```c
int foo() {
    int a; // lzh-warning {{unused variable 'a'}}
    return 0; 
}
```

Clang 验证命令如下:

```shell
clang -cc1 -Wall -Wextra -verify=lzh -x c test.c
# OR
clang -Xclang -verify=lzh -Wall -Wextra -c test.c
```

删除上述注释, 当验证失败时, 会有类似于如下的提示:

```plain
error: no expected directives found: consider use of 'expected-no-diagnostics'
error: 'warning' diagnostics seen but not expected: 
  File test.c Line 2: unused variable 'a'
2 errors generated.
```







## CMake

### 在 Ubuntu 中通过 PPA 安装最新版本的 CMake

```shell
sudo apt-get purge cmake
sudo apt-get install apt-transport-https ca-certificates gnupg software-properties-common wget
wget -O - https://apt.kitware.com/keys/kitware-archive-latest.asc 2>/dev/null | gpg --dearmor - | sudo tee /etc/apt/trusted.gpg.d/kitware.gpg >/dev/null
sudo apt-add-repository 'deb https://apt.kitware.com/ubuntu/ focal main'
sudo apt-get update
sudo apt-get install -y cmake
```





### CMake 核心函数

| 函数名                                                    | 功能                                                         | 示例                                                         |
| --------------------------------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| project(<项目名称>)                                       | 配置项目名称                                                 | `project(clang-plugin-xxx)`                                  |
| set(<CMake环境变量名称> <值>)                             | 设置 CMake 环境变量                                          | `set(CMAKE_CXX_STANDARD 17 CACHE STRING "")`                 |
| find_package(<包名称>)                                    | 在 `CMAKE_MODULE_PATH` 或 `CMAKE_PREFIX_PATH` 中搜索指定名称的 CMake 库.**Module模式**下搜索`FindXXX.cmake`; **Config模式**下搜索`XXXConfig.cmake` | `find_package(Clang REQUIRED CONFIG)`                        |
| include_directories(<库名称> <目录>)                      | 设置头文件搜索目录                                           | `include_directories(SYSTEM "${LLVM_INCLUDE_DIRS};${CLANG_INCLUDE_DIRS}")` |
| include(<模块名称>)                                       | 引入 CMake 模块                                              | `include(CheckCXXCompilerFlag)`                              |
| add_library(<目标名> <类型> <源文件>)                     | 添加一个编译目标, 目标类型为 Library                         | `add_library(xxx SHARED xxx.cc)`                             |
| target_link_options(<目标名> <选项可见范围> <options...>) | 为指定的目标添加链接选项                                     | `target_link_options(xxx PRIVATE -undefined dynamic_lookup)` |





### 使用 CMake 构建过程中查看详细的编译命令与链接命令

- CMake 输出详细的**编译**命令:

```shell
cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..
```

CMake 在构建项目编译文件时可以通过开启 `CMAKE_EXPORT_COMPILE_COMMANDS` CMake 选项输出 `compile_commands.json` 编译过程文件.

值得注意的是, **该方法仅能查看编译过程, 而不能查看链接过程. **

- CMake 获取**链接**命令:

    - 方法一: 进入对应文件的编译目录, 查看 `link.txt` 文件获取详细的链接过程, 如

        ```shell
        $ cat build/CMakeFiles/HelloWorld.dir/link.txt
        /usr/bin/c++ -fPIC  -fno-rtti -fvisibility-inlines-hidden -shared -Wl,-soname,libHelloWorld.so -o libHelloWorld.so CMakeFiles/HelloWorld.dir/HelloWorld.cpp.o 
        ```

    - 编译时开启 CMake `VERBOSE` 选项, 输出编译过程中执行命令的详细信息, 如:

        ```shell
        cmake --build . -- VERBOSE=1
        # OR
        cmake --build . --verbose
        # OR
        cmake --build . -v
        ```

        可以看到如下字段:

        ```plain
        [100%] Linking CXX shared library libHelloWorld.so
        /usr/bin/cmake -E cmake_link_script CMakeFiles/HelloWorld.dir/link.txt --verbose=1
        /usr/bin/c++ -fPIC  -fno-rtti -fvisibility-inlines-hidden -shared -Wl,-soname,libHelloWorld.so -o libHelloWorld.so CMakeFiles/HelloWorld.dir/HelloWorld.cpp.o 
        ```

        



一个 `CMake` 示例:

```cmake

#  cmake ..
#  cmake --build . -- VERBOSE=1

# =====================================================================
# 0. 环境要求
# =====================================================================
cmake_minimum_required(VERSION 3.20)
project(clang-plugin-print-function-names)
# 0.0 编译时输出 compile_commands.json 文件
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(CMAKE_C_COMPILER "/usr/local/llvm/bin/clang")
set(CMAKE_CXX_COMPILER "/usr/local/llvm/bin/clang++")


# =====================================================================
# 1. 加载 Clang 有关配置
# =====================================================================
# 1.1 此命令用于初始化一个 cmake 缓存变量 CT_Clang_INSTALL_DIR 为 ""
#     用于存储 Clang 的安装目录
#        - CT_Clang_INSTALL_DIR: 变量名
#        - "": 默认值
#        - CACHE: 开启 CMake 缓存
#        - PATH: 表示该变量类型为路径
#        - "Clang installation directory": 该变量的描述
set(CT_Clang_INSTALL_DIR "" CACHE PATH "Clang installation directory")

# 1.2 修改表示 cmake 搜索路径的环境变量 CMAKE_PREFIX_PATH, 以便 find_package 函数
#     能够找到 ${llvm}/lib/cmake/clang/ClangConfig.cmake 文件
list(APPEND CMAKE_PREFIX_PATH "${CT_Clang_INSTALL_DIR}/lib/cmake/clang/")

# 1.3 在 CMake 配置文件中查找名为 "Clang" 的包, 并配置其为 REQUIRED 以在找不到时报错
#     使用 CONFIG 模式查找所有名为 "*Config.cmake"的包
find_package(Clang REQUIRED CONFIG)

# 1.4 加载 Clang 包后, 会自动配置 LLVM_VERSION_MAJOR 变量
#     判断当前的 Clang 版本是否满足编译要求
if("${LLVM_VERSION_MAJOR}" VERSION_LESS 12)
  message(FATAL_ERROR "Found LLVM ${LLVM_VERSION_MAJOR}, but need LLVM 12 or above")
endif()

# HelloWorld includes headers from LLVM and Clang - update the include paths
# accordingly
# 1.5 配置 CMake 项目的头文件搜索路径
#     - SYSTEM: 关键词, 表示此处添加的路径为系统库路径, 编译时不会对这些库产生警告信息(假定系统库都是正确的)
#     - 后续的参数指定了 Clang 与 LLVM 的头文件路径, 使用分号 ";" 分割
include_directories(SYSTEM "${LLVM_INCLUDE_DIRS};${CLANG_INCLUDE_DIRS}")


# =====================================================================
# 2. Clang-Plugin 的 Build 配置选项
# =====================================================================
# 2.1 LLVM 使用了 C++ 17 标准, 编写插件时使用相同的 C++ 标准
#     https://llvm.org/docs/CodingStandards.html#c-standard-versions
set(CMAKE_CXX_STANDARD 17 CACHE STRING "")

# 2.2 LLVM 在常规的编译过程中禁用了 RTTI(运行时类型信息), 
#     如果没有启用 rtti 则在编译时添加 `-fno-rtti` 选项
if(NOT LLVM_ENABLE_RTTI)
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti")
endif()

# 2.3 引入 CMake 标准模块 CheckCXXCompilerFlag, 以使用 check_cxx_compiler_flag 函数
include(CheckCXXCompilerFlag)
#     检查编译器是否支持 -fvisibility-inlines-hidden 选项
#     并将结果设置到全局变量 SUPPORTS_FVISIBILITY_INLINES_HIDDEN_FLAG 中(支持为 1, 不支持为 0)
check_cxx_compiler_flag("-fvisibility-inlines-hidden"
  SUPPORTS_FVISIBILITY_INLINES_HIDDEN_FLAG)
#      如果当前的编译器支持 -fvisibility-inlines-hidden, 则在 CXX_FLAGS 中添加该选项
if(${SUPPORTS_FVISIBILITY_INLINES_HIDDEN_FLAG} EQUAL "1")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fvisibility-inlines-hidden")
endif()


# =====================================================================
# 3. 添加编译目标
# =====================================================================
# 3.1 
add_library(xxx SHARED PrintFunctionNames.cc)
target_include_directories(xxx PUBLIC src)

# 在 MacOS 中使用 undefined symbols 选项
if(${CMAKE_SYSTEM_NAME} STREQUAL "Darwin")
    target_link_options(xxx PRIVATE -undefined dynamic_lookup)
endif()
# target_link_libraries(xxx
#   "$<$<PLATFORM_ID:Darwin>:-undefined dynamic_lookup>")




# # 使用 message 输出结果
# # 获取编译选项
# get_target_property(MY_COMPILE_OPTIONS xxx COMPILE_OPTIONS)
# message("编译选项：${MY_COMPILE_OPTIONS}")
 
# # 获取链接选项
# get_target_property(MY_LINK_LIBRARIES xxx LINK_LIBRARIES)
# message("链接选项：${MY_LINK_LIBRARIES}")
```

































