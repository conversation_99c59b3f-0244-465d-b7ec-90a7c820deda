# 文本处理

本文档整理了 Linux 系统中常用的文本处理命令和技巧。

## 文本快速替换

### 使用 sed 进行文本替换

```shell
# 将文件中的http替换为https
# "s@<reg_source>@<str_target>g"
sed -i "s@http@https@g" <file>
```

## JSON 数据处理

### SHELL 处理 JSON 数据

解析当前目录下所有的 `JSON` 文件，提取其中的 `count` 字段，并将结果进行输出。

```shell
total=0; for file in *.json; do count=$(jq -r '.count' "$file"); total=$((total+count)); done; echo $total
```

## AWK 文本处理

### 基本 AWK 用法

```shell
# grep 搜索包含指定字符串的行
# awk 是文本处理语言, 可以处理非常复杂的文本格式. 
#     格式为 awk '匹配模式 {命中行为}'
#     NR==1 表示匹配输入文件的第一行内容
#     {print $2} 表示获取输入中的第二个参数($0表示当前行本身), ps 命令中第二个参数为`进程id`
#     可以使用 `-F,` 指定分隔符为 ",", 或使用 `-F'|'` 指定分隔符为 "|"

# 也可以不使用 grep 直接使用 awk完成指定进程的筛选
# ps aux | awk '/afl-fuzz-llvm/ {lineno++; if(lineno==1) print $2}'
```

### 实际应用示例

```shell
# 获取特定进程的 PID
ps aux | grep 'afl-fuzz-llvm' | awk 'NR==1{print $2}'

# 使用 awk 进行更复杂的文本处理
ps aux | awk '/afl-fuzz-llvm/ {lineno++; if(lineno==1) print $2}'
```

## 查看标准库函数原型

### 使用 man 命令

`man` 命令用于显示手册页，手册页分为不同的章节，每个章节涵盖不同类型的信息。`man 3` 中的 `3` 指的是第三章节，这一章节包含的是库函数（Library functions）的文档。

#### 手册页章节说明

1. **第1章**：用户命令（User Commands）
2. **第2章**：系统调用（System Calls）
3. **第3章**：库函数（Library Functions）
4. **第4章**：设备文件和特殊文件（Special Files and Drivers）
5. **第5章**：文件格式和约定（File Formats and Conventions）
6. **第6章**：游戏和趣味（Games and Screensavers）
7. **第7章**：杂项（Miscellaneous），例如宏包、协议、标准等
8. **第8章**：系统管理命令（System Administration Commands）和守护进程（Daemon）

使用 `man 3` 可以指定查看库函数的手册页。例如：

```bash
man 3 free
```

这条命令会显示 `free` 函数在第三章节中的手册页，提供函数的原型、描述、参数、返回值和其他相关信息。

## 相关链接

- [[文件和目录操作]]
- [[进程和系统监控]]
- [[编译和调试]]
