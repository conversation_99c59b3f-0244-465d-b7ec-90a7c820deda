<GeminiOptimizationFrom>Database/Neo4j.md</GeminiOptimizationFrom>
# Neo4j 使用说明

---





## 安装

[Neo4j 5.3.0 Linux 下载链接](https://dist.neo4j.org/neo4j-community-5.3.0-unix.tar.gz)

[Neo4j 5.3.0 Windows 下载链接](https://dist.neo4j.org/neo4j-community-5.3.0-windows.zip)

依赖`Java 17`

[Microsoft Build OpenJDK](https://learn.microsoft.com/zh-cn/java/openjdk/download)

> https://aka.ms/download-jdk/microsoft-jdk-17.0.5-linux-x64.tar.gz



## 使用

将`${NEO4J}/conf/neo4j.conf`中的以下配置打开:

```config
server.default_listen_address=0.0.0.0
```

运行如下命令启动服务:

```bash
(bash) ${NEO4J}/bin/neo4j console
```

浏览器访问`http://<ip>:7474`查看数据库



## Python 接口



**安装**

```bash
(bash) pip install py2neo
```

**样例**

```python
from py2neo import Graph, Node, Relationship, Subgraph, NodeMatcher

URI = "neo4j://0.0.0.0:7687"
AUTH = ("neo4j", "neo4j")
# 连接远程 Neo4j
graph = Graph(URI, auth=AUTH)
# 删除图中的所有节点
graph.delete_all()
# 创建节点
n1 = Node("Person", name="Jack")
n2 = Node("Person", name="Tom")
# 创建节点关系
r  = Relationship(n1, "Knows", n2)
# 在图中更新关系
graph.create(r)
# 建立子图
sub = Subgraph(relationships=[r, ])
# 在 graph 中创建子图
graph.create(sub)

# 查找节点
find_all_person = graph.nodes.match("Person")

# 更新所有节点
tx = graph.begin()
# 创建节点匹配器
matcher = NodeMatcher(graph)
# 搜索指定 Label 的节点
nodes = matcher.match("Person")
# 将返回的“Match”类转成list
new_nodes = list(nodes)
## 添加你要修改的东西
for node in new_nodes:
    node['name'] = "Hello"
    node['age'] = 666
# 里面是Node的list可以作为Subgraph的参数
sub = Subgraph(nodes=new_nodes)
# 调用push更新
tx.push(sub)
graph.commit(tx)
```

