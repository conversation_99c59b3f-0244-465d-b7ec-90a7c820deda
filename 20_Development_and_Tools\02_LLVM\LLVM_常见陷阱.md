<GeminiOptimizationFrom>LLVM/LLVMPass-一些不容易注意的坑.md</GeminiOptimizationFrom>
# LLVM 遇到的坑

---



#### 1. 关于 IR 中的"函数声明"

LLVM IR 中的函数"原型", 会依照当前 IR 文件中第一次调用该函数时参数进行定义. 当插入函数调用时, 如果没有明确参数的类型, 则很可能出现函数参数类型错误:

```
llc: error: llc: calc.ll:648:13: error: '@__runtime_after_line__' defined with type 'void (i32, [22 x i8]*)*' but expected 'void (i32, [12 x i8]*)*'
  call void @__runtime_after_line__(i32 74, [12 x i8]* @gvar_string_123)
```

在上述例子中, 第一次调用, `__runtime_after_line__`函数的第二个参数时参数类型为 `[22 x i8]*(即char[22])`, 而第二次调用该参数时类型为`[12 x i8]*(即char[12])`. **可以认为, IR 中函数的函数声明即第一次调用该函数时的定义**

出现该问题的原因时, 当我们传递全局变量时(GlobalVariable*), IR 会将全局变量的详细类型传递进去. 如 `char s[13]="Hello World!"` 在传递时, 不会作为 `char *`传递, 而是作为 `char[13]`传递. 此时会出现参数错误.

可以通过 `llvm::IRBuilder<>::CreateBitCast` 进行类型转换, 可以将全局变量转换为指针类型.

```cpp
LLVMContext& Ctx;
llvm::IRBuilder<> builder(Ctx);
llvm::Value* v = builder.CreateBitCast(GlobalVariable*, llvm::Type);
```



#### 2. PHI 节点位置

当遇到如下错误时, 表示 PHI 节点没有被正确的放在基本块起点. 

```
PHI nodes not grouped at top of basic block!
  %212 = phi i1 [ false, %202 ], [ %210, %206 ], !dbg !403
label %211
PHI nodes not grouped at top of basic block!
  %240 = phi i1 [ false, %230 ], [ %238, %234 ], !dbg !437
label %239
...
```

PHI节点是LLVM的中间表示语言（LLVM IR）中的一种特殊节点，用于实现静态单赋值（Static Single Assignment）表达式的一部分。PHI节点主要出现在基本块（basic block）的开始，用于根据控制流的来源选择一个值进行赋值。

**因此在插入指令时, 不能在 PHI 节点前插入指令**



#### 3. 获取当前变量的值传递关系

```cpp
for (llvm::User* user : I.users()) {
    user->print(errs());
    errs() << "\n";
}
```

















