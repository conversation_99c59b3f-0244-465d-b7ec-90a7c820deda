# 编译和调试

本文档整理了 Linux 系统中编译和调试相关的命令。

## 开发工具

### 查看动态链接库

```shell
ldd `which wget` | grep -E "tls|ssl|nss|nspr"
```

### 查看某个头文件的依赖

```shell
pacman -F /usr/include/asm/unistd.h
# 注意：这个命令在某些系统上可能不起作用
```

## 汇编编译

### 编译汇编代码

```assembly
; nasm -f elf64 get_cpu_info.S -o get_cpu_info.o 
; ld -m elf_x86_64 get_cpu_info.o -o get_cpu_info.elf

section .data
    my_symbol_output db 'Manufacturer: ', 0 ; 预留输出字符串

section .bss
    manufacturer resb 16 ; 分配12字节存储制造商信息+4字节存储回车

section .text
    global _start

_start:
    ; 获取CPU制造商信息
    mov eax, 0 ; cpuid的0号功能：获取制造商ID
    cpuid
    ; ebx, edx, ecx包含制造商ID
    mov [manufacturer], ebx
    mov [manufacturer+4], edx
    mov [manufacturer+8], ecx
    mov eax, 0x00000D0A
    mov [manufacturer+12], eax

    ; 准备输出制造商信息
    mov rax, 1 ; 系统调用号：sys_write
    mov rdi, 1 ; 文件描述符：1代表标准输出
    mov rsi, my_symbol_output ; 输出字符串的地址
    mov rdx, 14 ; 输出字符串的长度
    syscall ; 执行系统调用

    ; 输出制造商ID
    mov rax, 1 ; 系统调用号：sys_write
    mov rdi, 1 ; 文件描述符：1代表标准输出
    mov rsi, manufacturer ; 制造商ID的地址
    mov rdx, 16 ; 制造商ID的长度
    syscall ; 执行系统调用

    ; 退出程序
    mov rax, 60 ; 系统调用号：sys_exit
    xor rdi, rdi ; 退出状态码
    syscall ; 执行系统调用
```

#### 编译命令

```bash
# 32位汇编
nasm -f elf32 test.S -o test.o   
ld -m elf_i386 test.o -o test.elf
./test.elf  

# 64位汇编
nasm -f elf64 get_cpu_info.S -o get_cpu_info.o 
ld -m elf_x86_64 get_cpu_info.o -o get_cpu_info.elf
```

## 二进制分析

### objdump

将二进制程序导出为汇编代码：

```shell
objdump -S ./elf_file > elf.s
```

## 相关链接

- [[文本处理]]
- [[进程和系统监控]]
- [[系统配置]]
