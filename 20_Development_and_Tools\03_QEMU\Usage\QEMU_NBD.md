<GeminiOptimizationFrom>QEMU/QEMU NBD 网络块设备.md</GeminiOptimizationFrom>
# qemu-nbd 网络块设备

---



> https://www.qemu.org/docs/master/tools/qemu-nbd.html

QEMU NBD(Network Block Device), 能够将一个 QCOW 磁盘镜像使用 NBD 协议进行导出、绑定或连接, 使得我们能够访问 qcow 镜像中的文件系统. 

具体而言, 使用 qemu-nbd 访问 qcow 镜像中的文件系统主要步骤如下:

```shell
# 0. 在 Linux 内核中添加 nbd 模块, 添加后在 /dev 目录中才有 nbd<D> 块存在
sudo modprobe nbd
# 1. 使用 qemu-nbd 将指定的 <filename.qcow2> 文件连接到指定的 NBD 设备中
sudo qemu-nbd --connect=/dev/nbd0 <filename.qcow2>
# 2. 将块设备挂载到本地目录<dir>指定的挂载点中. 其中, 文件系统存在分区, nbd0p1 表示 块设备 nbd0 的 part 1 分区
sudo mount /dev/nbd0p1 <dir>
# 3. (可选) 修改文件后, 同步挂载目录
sync <dir>
# 4. 取消挂载块设备
df -h
sudo umount <dir>
# 5. 取消链接块设备 DEV
sudo qemu-nbd --disconnect=/dev/nbd0
```



针对使用 `luks(Linux Unified Key Setup, Linux 统一密钥设置)` 加密的文件系统, 可以通过Hook `/cores/grub/i386-pc/luks.mod` 模块中的 `grub_crypto_pbkdf2` 函数, 获取解密密钥进行解密. 具体而言:

```shell
# 1. 修改/cores/grub目录, 破坏 Grub 启动引导
sudo mount /dev/nbd0p3 /dev/qemu-nbd0-part3-grub
find /dev/qemu-nbd0-part3-grub/coreos/grub/ -type d | xargs -I {} -- mv {} {}-bak

# 2. 在 Grub 中手动加载解密相关的内核模块
(in-qemu-grub) insmod luks
(in-qemu-grub) insmod /coreos/grub/i386-pc/gcry_rijndael.mod
(in-qemu-grub) insmod /coreos/grub/i386-pc/gcry_sha256.mod
(in-qemu-grub) insmod /coreos/grub/i386-pc/ext2.mod

# 3. 使用使用 IDA 定位关键解密函数 grub_crypto_pbkdf2 的调用位置
# 通过关键字符串进行定位

# 4. 使用 GDB 搜索解密函数调用位置, 并下断点, 导出密钥
(gdb) find /b 0,0x8000000000,0x51, 0x52, 0x6A, 0x20, 0xFF, 0xB5, 0x14, 0xFB, 0xFF, 0xFF
# 会搜索到两个地址, 一个低地址的静态地址, 一个高地址的真实加载的内存地址
(gdb) x /30i 0xbff56a19
(gdb) break *0xbff56a19
(gdb) dump binary memory <key_path> $rdx $rdx+52

# 5. 使用密钥解密加密的 NBD 块
cat <key_path> | sudo cryptsetup luksOpen /dev/nbd0p9 part9
ls /dev/mapper # part9

# 6. 挂载解密后映射的块设备
sudo su # 必须进入root权限挂载到 /dev 否则会出错
mount /dev/mapper/part9 /dev/qemu-nbd-part9

# 7. 清理工作...
sync
sudo umount /dev/qemu-nbd-part9
sudo cryptsetup luksClose part9
```







