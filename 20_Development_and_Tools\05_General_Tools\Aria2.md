<GeminiOptimizationFrom>Tools/Aria2.md</GeminiOptimizationFrom>
# Aria2

---





> https://hub.docker.com/r/p3terx/aria2-pro
>
> https://ariang.mayswind.net/latest/#!/downloading



```bash
docker run -d \
    --name aria2-pro \
    --restart unless-stopped \
    --log-opt max-size=1m \
    -e PUID=$UID \
    -e PGID=$GID \
    -e UMASK_SET=022 \
    -e RPC_SECRET=qweqwe123 \
    -e RPC_PORT=6800 \
    -p 6800:6800 \
    -e LISTEN_PORT=6888 \
    -p 6888:6888 \
    -p 6888:6888/udp \
    -v $PWD/aria2-config:/config \
    -v $PWD/aria2-downloads:/downloads \
    p3terx/aria2-pro
```



