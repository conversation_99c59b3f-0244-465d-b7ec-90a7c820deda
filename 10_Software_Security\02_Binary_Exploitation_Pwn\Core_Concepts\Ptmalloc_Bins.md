<GeminiOptimizationFrom>CTF-PWN/ptmalloc-bins.md</GeminiOptimizationFrom>
# PTmalloc Bins

---



> https://ctf-wiki.org/pwn/linux/user-mode/heap/ptmalloc2/heap-structure/#bin



### 一般的 Bins

```c
#define NBINS             128
struct malloc_state
{
  ...
  /* Normal bins packed as described above */
  mchunkptr bins[NBINS * 2 - 2];
  ...
}
```



通用的 Chunk 结构如下:

```c
struct malloc_chunk {

  INTERNAL_SIZE_T      mchunk_prev_size;  /* Size of previous chunk (if free).  */
  INTERNAL_SIZE_T      mchunk_size;       /* Size in bytes, including overhead. */

  struct malloc_chunk* fd;         /* double links -- used only if free. */
  struct malloc_chunk* bk;

  /* Only used for large blocks: pointer to next larger size.  */
  struct malloc_chunk* fd_nextsize; /* double links -- used only if free. */
  struct malloc_chunk* bk_nextsize;
};
```





| Bin Type     | Arch 32                        | Arch x64                          | Describe |
| ------------ | ------------------------------ | --------------------------------- | -------- |
| Fast Bin     | 0 ~ 64(Default) or 80(Maximum) | 0 ~ 128(Default) or 160 (Maximum) |          |
| Small Bin    | 16 ~ 504                       | 32 ~ 1016                         |          |
| Large Bin    |                                |                                   |          |
| Unsorted Bin |                                |                                   |          |





### **Fast Bin**

```c
#define NFASTBINS  (fastbin_index (request2size (MAX_FAST_SIZE)) + 1)
struct malloc_state
{
  ...
  /* Fastbins */
  // 不论 32 位 还是 64 位 NFASTBINS 长度都是 10
  // https://www.cnblogs.com/husterlong/p/14674040.html
  mfastbinptr fastbinsY[NFASTBINS];
  ...
}

#ifndef DEFAULT_MXFAST
#define DEFAULT_MXFAST     (64 * SIZE_SZ / 4)
#endif

/* The maximum fastbin request size we support */
#define MAX_FAST_SIZE     (80 * SIZE_SZ / 4)
```

- **fast bin** 不会发生 `分割`、`合并`以及中间检查
- LIFO 策略, 即最后释放的 Chunk 会被最早的分配
- 最多支持 10 个(NFASTBINS) Fast bins
- ptmalloc 会调用 `#define set_max_fast(s)` 宏设置 `global_max_fast ` 大小, 默认大小为 64 或 128 
- **fast bin chunk** 中的 `inuse` 字段始终被设置为 `1`, 因此不会发生 **chunk 合并**



### Small Bin

- `chunk_size = 2 * SIZE_SZ * index`
- **Small Bins** 中有 62 个循环双向链表, 每个链表中的 chunk_size 都相同
- **Small Bins **中的 bin 采用 **FIFO** 的规则



| 下标 | SIZE_SZ=4（32 位） | SIZE_SZ=8（64 位） |
| :--- | :----------------- | :----------------- |
| 2    | 16                 | 32                 |
| 3    | 24                 | 48                 |
| 4    | 32                 | 64                 |
| 5    | 40                 | 80                 |
| x    | 2*4*x              | 2*8*x              |
| 63   | 504                | 1008               |



```c
#define NSMALLBINS 64

#define smallbin_index(sz)                                                     \
    ((SMALLBIN_WIDTH == 16 ? (((unsigned) (sz)) >> 4)                          \
                           : (((unsigned) (sz)) >> 3)) +                       \
     SMALLBIN_CORRECTION)
```

















