<GeminiOptimizationFrom>Sanitizer/ThreadSanitizer.md</GeminiOptimizationFrom>
# ThreadSanitizer

---



> https://static.googleusercontent.com/media/research.google.com/en//pubs/archive/35604.pdf
>
> https://zhuanlan.zhihu.com/p/38687826
>
> https://archive.ph/71oyY
>
> https://zhuanlan.zhihu.com/p/27503041
>
> https://archive.ph/iAutx



### 简介

`ThreadSanitizer`（简称TSan）是一种动态分析工具，可以帮助开发人员检测并发程序中的`数据竞争`（data race）问题。什么是数据竞争呢?数据竞争是指

1. 两个或多个线程访问同一内存位置，
2. 并且至少有一个线程正在写入该位置，
3. 并且没有使用同步原语来保证访问的顺序



`ThreadSanitizer` 使用**内存访问历史记录**来检测数据竞争，并输出数据竞争的详细信息，包括哪些线程访问了哪些内存位置，以及在哪些行为中发生了数据竞争。它可以帮助开发人员快速诊断并发程序中的数据竞争问题，从而减少调试时间和错误修复时间。

需要注意的是，`ThreadSanitizer` 只能检测到并发程序中的数据竞争问题，不能保证程序的正确性。下面是 `ThreadSanitizer` 大致工作原理：

1. `插桩 Instrument`：首先，`ThreadSanitizer` 会在程序的中插入额外的代码，以便跟踪程序中所有的内存访问操作。
2. `记录内存访问历史`：当程序运行时，`ThreadSanitizer` 会记录每个线程的内存访问操作历史，包括读、写、锁定和解锁等操作。`ThreadSanitizer` 通过记录这些历史信息来分析线程之间的交互和竞争情况。
3. `检测数据竞争`：当两个或多个线程同时访问同一内存位置，并且至少有一个线程正在写入该位置时，`ThreadSanitizer` 会检测到数据竞争问题，并报告相应的信息。`ThreadSanitizer` 还会跟踪并报告由数据竞争引起的任何未定义行为，例如使用未初始化的内存、释放非法的内存等。
4. `报告生成`：最后，`ThreadSanitizer` 会生成一个报告，其中包含所有检测到的数据竞争问题的详细信息。这个报告可以帮助开发人员诊断并发程序中的数据竞争问题，并快速定位和修复这些问题。



### 插桩

`ThreadSanitizer` (TSan) 在编译时会对以下情况的代码进行插桩，以检测并报告数据竞争问题：

1. 所有的读写内存访问操作（包括原子操作）。
2. 所有的锁操作（如 `pthread_mutex_lock`、`pthread_mutex_unlock`、`pthread_rwlock_rdlock` 等）。
3. 所有的条件变量操作（如 `pthread_cond_wait`、`pthread_cond_signal` 等）。
4. 所有的线程创建和销毁操作（如 `pthread_create`、`pthread_exit` 等）。
5. 所有的同步原语操作（如 `atomic_compare_exchange`、`__sync_fetch_and_add` 等）。
6. 所有的信号量操作（如 `sem_wait`、`sem_post` 等）。

对这些代码进行插桩的目的是监视所有并发操作，并通过比较内存访问序列来检测潜在的数据竞争问题。



![image-20230220154943871](https://qiniu.maikebuke.com/image-20230220154943871.png)



`ThreadSanitizer` 本质是一个状态机。在程序执行期间，它会一直观测各类**事件**(*event*)，并维护了一个**全局的**事件队列。每当一个新的事件发生时，`ThreadSanitizer` 会将其加入到事件队列中，并更新自己的状态，然后通过状态判断这个事件是否导致了`data race`。



### 一些概念

| 概念               | 人话                                                   | 说明                                                         |
| ------------------ | ------------------------------------------------------ | ------------------------------------------------------------ |
| Tid                |                                                        | Thread ID, 线程ID                                            |
| ID                 |                                                        | 内存地址, 类似于指针, 一个ID对应一个byte(污点传播中的bit map) |
| EventType          |                                                        | 事件类型, 为包含的六种线程事件, 包括`(Read, Wtire, Lock, Unlock, Signal, Wait)` |
| Event              |                                                        | 即事件, 本质为`(EventType, Tid, ID)`的三元组, 记作$EventType_{Tid}(ID)$ |
| 事件队列           |                                                        | 衍生概念, `ThreadSanitizer` 会记录下上述六种事件的所有操作, 每当新的事件发生时, 当前事件会被追加到一个全局队列的尾部. |
| L                  | $Lock_{Tid}(L)$                                        | `Lock`, 即锁. 本质上为一个出现锁事件的内存ID. 若事件队列中的 $Lock_{Tid}(L)$ 数目大于 $Unlock_{Tid}(L)$ 的数目, 则当前锁 `L` 被线程 `Tid` 持有(held). 此处包含了两类持有, 分别是: `read-held`, 与 `write-held` . |
| LS                 |                                                        | `Lock Set`, 即锁集. 为某个线程持有的所有锁的集合.            |
| Event Context      |                                                        | 事件上下文, 即线程中的 `Stack Trace`, 只用于最后的生成报告   |
| Segment            |                                                        | 本质上为一个线程内的一段只包含内存操作的事件流, 任何一个内存操作事件属于且仅属于一个Segment, 且一个 Segment 只能属于某个线程. |
| Happens-before arc | 同一个信号量保证的访问顺序                             | 即`Happens-before 弧`. 对于一组事件$\begin{cases}X=Signal_{Tx}(A_x)\\Y=Wait_{Ty}(A_y)\end{cases}$, 若 $(A_x==A_y)\And(T_x!=T_y)$, 此时如果事件$X$ 先被`tsan`观察得到, 则$(X,Y)$ 构成`Happens-before arc` |
| Happens-before     | $X \prec Y$, 表示两个事件的发生顺序关系                | 对于两个事件$\begin{cases}X=EventType_{Tx}(A_x)\\Y=EventType_{Ty}(A_y)\end{cases}$, 如果事件$X$先被观测到, 则符合以下任意条件时$X \prec Y$成立(读作X happens-before Y): 1. $T_X=T_Y$; 2. $arc(X,Y)$构成弧; 3. $\exists {E_1,E_2}:X \prec E_1 \prec E_2 \prec Y$; **值得注意的是,Happens-before表示X一定先于Y发生!!!不存在偶然性** |
| SS                 |                                                        | Segment Set, 对于一个$SS=\left \{ S_1,S_2, ..., S_n \right \}$而言, 满足$\forall i,j; S_i \npreceq S_j$. |
| Concurrent         | 并行的两个事件之间既没有Happens-before依赖, 也不存在锁 | 并行性, 对于事件$X,Y$, 若1. $X \npreceq Y$; 2. $Y \npreceq X$; 3. $LS(X)\cap LS(Y)=\emptyset$; 则说明两个事件为并行的. |
| Data Race          |                                                        | 即数据竞争, 两个及以上的线程并行的操作了同一个内存地址ID，并且至少其中一个内存操作事件是Write |



![img](https://qiniu.maikebuke.com/v2-0d1769b7257f5d30787d34392a938f74_1440w.webp)

1. $S_1\prec S_4$, 由于$S_1$与$S_4$发生在一个线程中, 所以二者的执行顺序是确定的, 存在`Happens-before`关系;
2. $S_1\prec S_3$, 由于$S_1$与$S_3$之间存在一个同步信号量, 是一个$(Signal, Wait)$弧, 符合`Happens-before arc`规则, 因此存在`Happens-before`关系;
3. $S_1$与$S_2$之间不满足任何的`Happens-before`关系, 因此$S1 \npreceq S2 \land S2 \npreceq S1$, 基于此二者可以构成一个`Segment Set`$\{S_1,S_2\}$;
4. 同理, 片段集合还有$\{S_3,S_4\}$, $\{S_2,S_4\}$;



























