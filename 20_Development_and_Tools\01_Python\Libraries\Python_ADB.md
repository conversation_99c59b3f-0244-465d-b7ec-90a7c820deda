<GeminiOptimizationFrom>Python/ADB.md</GeminiOptimizationFrom>
# ADB With Python

---



```shell
# 查看所有连接的设备
$ adb devices
```





```python
import cv2
import numpy as np
import subprocess


# 获取屏幕截图 (屏幕应该处于打开状态)
def adb_get_screen():
    # 使用 ADB 命令获取屏幕实时图像数据
    try:
        result = subprocess.run(['adb', 'exec-out', 'screencap', '-p'], capture_output=True)
        if result.returncode == 0:
            screenshot = result.stdout
            # 将二进制数据转换为图像
            image = cv2.imdecode(np.frombuffer(screenshot, np.uint8), cv2.IMREAD_COLOR)
            return image
    except subprocess.CalledProcessError as e:
        print(e.output)
    return None

# 点击屏幕的指定坐标
def adb_click(x, y):
    # 使用 ADB 发送点击事件
    subprocess.run(['adb', 'shell', 'input', 'tap', str(x), str(y)])

```









