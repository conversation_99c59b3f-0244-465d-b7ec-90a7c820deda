<GeminiOptimizationFrom>DynamicAnalysis/PANDA-re.md</GeminiOptimizationFrom>
# pandare/panda

---



> Github: https://github.com/panda-re/panda
>
> 我的 PR: https://github.com/panda-re/panda/pull/1266
>
> 我的commit: https://github.com/panda-re/panda/commit/605822fdf6b133f6663c418dec4a33795746f70f
>
> 一个可用的docker: https://hub.docker.com/layers/pandare/panda/latest/images/sha256-15a23e4b821a094a79caebd77e3095a7efbc82f108a30cfde1067759f72f9377

## 本地安装

安装前请准备一个全新的镜像

只能在 **Ubuntu Server** 中安装, **不能够在Desktop版本的系统中安装**, 安装步骤如下:

```bash
wget https://github.com/panda-re/panda/raw/dev/panda/scripts/install_ubuntu.sh
chmod +x install_ubuntu.sh
# 该过程会克隆仓库, 请确保 github 网络通畅
./install_ubuntu.sh
# 验证是否安装成功
python3 -c 'import pandare; panda = pandare.Panda(generic='\''i386'\'')'
```



## PyPanda

```python
from pandare import Panda
panda = Panda(generic="i386")

@panda.queue_blocking
def do_stuff():
    panda.revert_sync("root")
    print(panda.run_serial_cmd("uname -a",no_timeout=True))
    panda.end_analysis()

# @panda.hook_symbol("libc-", "fwrite", name=program_name)
    
panda.run()
```







## 使用 Docker









