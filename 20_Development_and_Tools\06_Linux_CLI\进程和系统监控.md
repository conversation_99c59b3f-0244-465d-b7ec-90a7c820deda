# 进程和系统监控

本文档整理了 Linux 系统中进程管理和系统监控相关的命令。

## 进程管理

### 杀死进程

```shell
# 杀死所有指定用户的进程
killall -u <username>

# 杀死所有启动命令中包含 "vscode-server" 的进程
ps aux | grep vscode-server | awk '{print $2}' | xargs sudo kill -9
pkill -9 -f vscode-server
```

### 进程调试

#### GDB attach 到指定进程

```shell
gdb -ex "set confirm off" attach $(ps aux | grep 'afl-fuzz-llvm' | awk 'NR==1{print $2}')
```

### 进程优先级管理

在Linux中，进程的优先级（又称niceness）的范围是-20到19，其中-20表示最高优先级，19表示最低优先级。进程的默认优先级通常为0。

#### 设置进程优先级

1. **启动进程时设置优先级**，使用 `nice` 命令：

```bash
# 设置进程的nice值为-10
nice -n -10 ./my_program
```

2. **修改已运行进程的优先级**，使用 `renice` 命令：

```bash
# 将进程的优先级提高到-10
renice -n -10 <pid>
```

3. **设置用户的线程优先级**：

```bash
sudo renice -n -10 -u <用户名>
```

## 系统资源监控

### 查看资源占用

| 功能                      | 命令                                             | 说明                          |
| ------------------------- | ------------------------------------------------ | ----------------------------- |
| 查看内存占用              | free -h                                          | Mem: 物理内存；Swap：交换内存 |
| 查看CPU占用最多的10个进程 | ps auxw\|head -1;ps auxw\|sort -rn -k3\|head -10 | -k3 按照第三列排序            |
| 查看系统资源占用          | htop                                             | sudo apt install htop         |
| 查看磁盘占用              | df -h                                            | -hl查看剩余空间               |
| 查看目录大小              | du -sh <dir>                                     |                               |
| 查看目录中的文件数        | du -sm <dir>                                     |                               |
| 查看磁盘IO使用            | iostat -d -x -k 1 10                             |                               |

### Python 系统监控

Python中可以使用 `psutil` 模块进行系统监控：

```bash
pip install psutil
```

```python
import os
import psutil
pid = os.getpid()
p = psutil.Process(pid)
# 一些使用示例
p.pid
p.name()
p.cpu_percent()
p.memory_info()
p.memory_percent()
p.cmdline()
```

## 进程内存分析

### 查看指定进程的内存映射

```shell
pmap -X `pidof python3`
cat /proc/`pidof python3`/maps
```

## 网络监控

### 查看网络连接

```shell
# 查看所有活动的网络连接
sudo ss -tunap
```

## 系统调用跟踪

### 记录运行的命令

```shell
strace -f -o my_script.strace -e trace=process my_script.sh
```

## 相关链接

- [[用户和权限]]
- [[系统配置]]
- [[文件和目录操作]]
