<GeminiOptimizationFrom>Tools/IDA-Python.md</GeminiOptimizationFrom>
# IDA Pro Python Script

---



> IDA Python 7.4 以后版本 API: https://x3h1n.github.io/2020/06/16/IDA-Python%E6%95%B4%E7%90%86/
>
> ​		https://web.archive.org/web/20231027070102/https://x3h1n.github.io/2020/06/16/IDA-Python%E6%95%B4%E7%90%86/
>
> IDA 反编译并修改部分反编译代码: https://blog.gentlecp.com/article/12309.html
>
> ​		https://web.archive.org/web/20231027070017/https://blog.gentlecp.com/article/12309.html
>
> 新旧版本API的对比: https://hex-rays.com/products/ida/support/ida74_idapython_no_bc695_porting_guide.shtml



## 基本模块

### `idc`

### `idaapi`

### `idautils`

### `ida_funcs`



## 使用 Python 脚本启动一个 IDA 窗口进行二进制分析

```python
# 命令行命令:
#     ida.exe -c -A -S<脚本目录> <目标二进制文件>
# 		-c: clean, 启动前删除旧的 IDA Database 
# 		-A: Autonomous, 自动模式, 启动时不显示 IDA Pro 主窗口
#		-S: Scripy, 指定分析的脚本


IDA_PATH = "C:\\Program Files\\IDA Pro 7.6\\ida.exe"

def analysis_with_python(script: str, sample: str):
    cmd = [IDA_PATH, "-c", "-A", f'-S"{script}"', sample]
    p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=1024)
    # p.poll() -> None: 运行中; 0: 正常退出; 1: sleep; 2: 进程不存在; 5: kill; 
    while p.poll() is None:
        stdout, stderr = p.communicate()
        if stdout:
            print(stdout.decode("UTF-8"))
        if stderr:
            print(stderr.decode("UTF-8"))
    return 
```







## Segment 相关

```python
import idc
import idautils

# 获取所有的 Segments 的起始地址
for seg_ea in idautils.Segments():
    # 获取指定地址所在的 Segment Name
    seg_name = idc.get_segm_name(seg_ea)
    # 获取指定地址所在的 Segment 的起始地址以及结束地址
    ea_start, ea_end = idc.get_segm_start(seg_ea), idc.get_segm_end(seg_ea)
    # 合法的地址不会是 idaapi.BADADDR
    assert(idaapi.BADADDR != ea_start)
    assert(idaapi.BADADDR != ea_end)
```



### 搜索某个段中的某个符号

```python
plt_range = None
# 1. 遍历程序中的所有 Segments, 判断其名称是否为 .plt
for seg_ea in idautils.Segments():
    seg_name = idc.get_segm_name(seg_ea)
    if seg_name == ".plt":
        plt_range = (idc.get_segm_start(seg_ea), idc.get_segm_end(seg_ea))

# 2. 从某个地址开始检索某个符号, 获取该符号所在的地址
recv_ea = idaapi.get_name_ea(plt_range[0], "recv")
assert(idaapi.BADADDR != recv_ea)

# 3. 检查当前符号所在的地址是否在目标段内
if (plt_range[0] <= recv_ea <= plt_range[1]):
    return idaapi.get_func(recv_ea)
else :
    return idaapi.BADADDR
```





## Function 相关



```python
import idaapi
import idautils

# 获取所有函数的起始地址
for func_ea in idautils.Functions():
    # 合法的地址不会是 idaapi.BADADDR
    assert(idaapi.BADADDR != func_ea)
    # 根据函数中的一个地址, 获取函数的 ida_funcs.func_t 对象
    func = idaapi.get_func(func_ea)
    # 根据 ida_funcs.func_t 对象, 获取函数的起始地址以及结束地址
    func_ea_start, func_ea_end = func.start_ea, func.end_ea
    # 根据函数中的一个地址, 获取当前函数名
    func_name = idaapi.get_func_name(func_ea)
```

获取函数的反编译代码

```python
# 获取指定函数的反汇编代码
def waji_util_get_func_pseudocode(func: ida_funcs.func_t) -> str:
    lines = []
    try:
        cfunc = idaapi.decompile(func.start_ea) # idaapi.ctree_visitor_t
    except Exception as e:
        fname = idaapi.get_func_name(func.start_ea)
        logger.error(f"IDA Decompile Error: {fname} -- {str(e)}")
        return ""
    # Source View
    sv = cfunc.get_pseudocode()
    for sline in sv:
        line = idaapi.tag_remove(sline.line)
        lines.append(str(line))
    return "\n".join(lines)
```





## IDA 交叉引用

```python

# 还有一个 api 是 idautils.CodeRefsFrom
# arg1: 被引用的地址
# arg2: ida_xref.XREF_ALL, ida_xref.XREF_FAR, ida_xref.XREF_DATA
for xref in idautils.XrefsTo(target_ea, ida_xref.XREF_ALL):
    print(xref.type, XrefTypeName(xref.type), 'from', hex(xref.frm), 'to', hex(xref.to))

```

其中, `xref.type` 的枚举值如下: 

```python
# Xref type names table
_ref_types = {
    ida_xref.fl_U  : 'Data_Unknown',
    ida_xref.dr_O  : 'Data_Offset',
    ida_xref.dr_W  : 'Data_Write',
    ida_xref.dr_R  : 'Data_Read',
    ida_xref.dr_T  : 'Data_Text',
    ida_xref.dr_I  : 'Data_Informational',
    ida_xref.fl_CF : 'Code_Far_Call',
    ida_xref.fl_CN : 'Code_Near_Call',
    ida_xref.fl_JF : 'Code_Far_Jump',
    ida_xref.fl_JN : 'Code_Near_Jump',
    20 : 'Code_User',
    ida_xref.fl_F : 'Ordinary_Flow'
}
```



获取一个地址的 `xrefs` 的所有汇编

<img src="https://qiniu.maikebuke.com/20231103105406.png" width="75%" />

```python
list(set([idc.GetDisasm(_.frm) for _ in idautils.XrefsTo(0x2D9C440)]))
```



### 迭代所有指令

```python
import re
import idaapi

start_ea = idaapi.inf_get_min_ea()
end_ea   = idaapi.inf_get_max_ea()

ea = start_ea
while (ea < end_ea):
    insn = idaapi.insn_t()
    decode_size = idaapi.decode_insn(insn, ea)
    asm = idc.GetDisasm(ea)
    if decode_size <= 0:
        ea += 1
        continue
    ea += decode_size
```



### 获取某个指令的类型以及参数

```python
ea = 0x1B2F28A
insn = idaapi.insn_t()
size = idaapi.decode_insn(insn, ea)
# 查看指令的类型
insn.itype == idaapi.NN_syscall
# 查看参数的类型, 查看其是否为寄存器
insn.Op1.type == idaapi.o_reg
idaapi.get_reg_name(insn.Op1.reg, 8) == "rax"
# 查看参数的类型, 查看其是否为立即数
insn.Op2.type == idaapi.o_imm
insn.Op2.value

# 获取某个地址的指令的字符串
asm = idc.GetDisasm(ea)
```

可以通过上面的方法从某个地址向前迭代:

```python

import idautils

idautils.DecodePreviousInstruction(<address>).ea
```





























































