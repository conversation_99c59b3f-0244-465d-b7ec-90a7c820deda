<GeminiOptimizationFrom>DynamicAnalysis/Intel Pin.md</GeminiOptimizationFrom>
# Intel Pin

---



[Intel主页](https://www.intel.com/content/www/us/en/developer/articles/tool/pin-a-dynamic-binary-instrumentation-tool.html)

[Intel下载页面](https://www.intel.com/content/www/us/en/developer/articles/tool/pin-a-binary-instrumentation-tool-downloads.html)



## Intel Pin Linux IA32 and Intel64

| Version  |       Date        |                             Kit                              |                          Signature                           |                        Documentation                         |                                                              |                                                              |
| :------: | :---------------: | :----------------------------------------------------------: | :----------------------------------------------------------: | :----------------------------------------------------------: | ------------------------------------------------------------ | ------------------------------------------------------------ |
| Pin 3.25 | October 20, 2022  | [98650](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.25-98650-g8f6168173-gcc-linux.tar.gz) | [sig](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.25-98650-g8f6168173-gcc-linux.tar.gz.sig) | [Manual](https://software.intel.com/sites/landingpage/pintool/docs/98650/Pin/doc/html/index.html) | [PinCRT](https://software.intel.com/sites/landingpage/pintool/docs/98650/PinCRT/PinCRT.pdf) | [Release Notes](https://software.intel.com/sites/landingpage/pintool/docs/98650/README) |
| Pin 3.24 |   July 18, 2022   | [98612](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.24-98612-g6bd5931f2-gcc-linux.tar.gz) | [sig](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.24-98612-g6bd5931f2-gcc-linux.tar.gz.sig) | [Manual](https://software.intel.com/sites/landingpage/pintool/docs/98612/Pin/doc/html/index.html) | [PinCRT](https://software.intel.com/sites/landingpage/pintool/docs/98612/PinCRT/PinCRT.pdf) | [Release Notes](https://software.intel.com/sites/landingpage/pintool/docs/98612/README) |
| Pin 3.23 |   May 12, 2022    | [98579](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.23-98579-gb15ab7903-gcc-linux.tar.gz) | [sig](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.23-98579-gb15ab7903-gcc-linux.tar.gz.sig) | [Manual](https://software.intel.com/sites/landingpage/pintool/docs/98579/Pin/doc/html/index.html) | [PinCRT](https://software.intel.com/sites/landingpage/pintool/docs/98579/PinCRT/PinCRT.pdf) | [Release Notes](https://software.intel.com/sites/landingpage/pintool/docs/98579/README) |
| Pin 3.22 | February 28, 2022 | [98547](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.22-98547-g7a303a835-gcc-linux.tar.gz) | [sig](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.22-98547-g7a303a835-gcc-linux.tar.gz.sig) | [Manual](https://software.intel.com/sites/landingpage/pintool/docs/98547/Pin/html/index.html) | [PinCRT](https://software.intel.com/sites/landingpage/pintool/docs/98547/PinCRT/PinCRT.pdf) | [Release Notes](https://software.intel.com/sites/landingpage/pintool/docs/98547/README) |
| Pin 3.21 | October 28, 2021  | [98484](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.21-98484-ge7cd811fd-gcc-linux.tar.gz) | [sig](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.21-98484-ge7cd811fd-gcc-linux.tar.gz.sig) | [Manual](https://software.intel.com/sites/landingpage/pintool/docs/98484/Pin/html/index.html) | [PinCRT](https://software.intel.com/sites/landingpage/pintool/docs/98484/PinCRT/PinCRT.pdf) | [Release Notes](https://software.intel.com/sites/landingpage/pintool/docs/98484/README) |
| Pin 3.20 |   July 12, 2021   | [98437](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.20-98437-gf02b61307-gcc-linux.tar.gz) | [sig](https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.20-98437-gf02b61307-gcc-linux.tar.gz.sig) | [Manual](https://software.intel.com/sites/landingpage/pintool/docs/98437/Pin/html/index.html) | [PinCRT](https://software.intel.com/sites/landingpage/pintool/docs/98437/PinCRT/PinCRT.pdf) | [Release Notes](https://software.intel.com/sites/landingpage/pintool/docs/98437/README) |

`wget https://software.intel.com/sites/landingpage/pintool/downloads/pin-3.25-98650-g8f6168173-gcc-linux.tar.gz`



## 基本使用

Pintools位置在`${PINHOME}/source/tools/`中

示例样本位于`${PINHOME}/source/tools/ManualExamples/`中

编译示例样本的命令:

```shell
cd ${PINHOME}/source/tools/ManualExamples/
# 编译 32 位 Pintool
make all TARGET=ia32
# 编译 64 位 Pintool
make all TARGET=intel64
```

使用`Pintool`

```shell
cd ${PINHOME}
./pin -t <dir_to_pin_tool.so> <pin_tool_args> -- <target_elf> <target_args>
# 示例
./pin -t source/tools/ManualExamples/obj-intel64/inscount0.so -o inscount0.log -- /bin/ls -a
```



### Pin 使用的 Make 变量

有关所有可用变量和标志的完整列表，请参阅 source/tools/Config/makefile.config. 一些常用的 `flag` 如下:

- `PIN_ROOT`: Specify the location for the Pin kit when building a tool outside of the kit.
- `CC`: Override the default c compiler for tools.
- `CXX`: Override the default c++ compiler for tools.
- `APP_CC`: Override the default c compiler for applications. If not defined, APP_CC will be the same as CC.
- `APP_CXX`: Override the default c++ compiler for applications. If not defined, APP_CXX will be the same as CXX.
- `TARGET`: Override the default target architecture e.g. for cross-compilation.
- `ICC`: Specify ICC=1 when building tools with the Intel Compiler.
- `DEBUG`: When DEBUG=1 is specified, debug information will be generated when building tools and applications. Also, no compilation and/or link optimizations will be performed.



## Pintool C++ 开发基本说明



```c
#include "pin.H"

// Pin 命令行选项 Handler, 用于注册 PINTOOL 所需的参数
// 此处表示接受一个 -o 选项
// https://software.intel.com/sites/landingpage/pintool/docs/98650/Pin/doc/html/group__KNOBS.html
KNOB< string > KnobOutputFile(KNOB_MODE_WRITEONCE, "pintool", "o", "inscount.out", "specify output file name");

// 该函数会在每条指令执行前运行
VOID docount() { icount++; }

// 每当 PIN 遇到一个新的指令时, 会调用此函数
// 此函数**并不是程序运行时执行的**, 而是指令运行前, PIN 对指令的处理
VOID Instruction(INS ins, VOID* v)
{
    // INS_InsertCall 将函数 docount() 插入指令执行前, 此后每运行当前指令都会调用插入的函数
    // https://software.intel.com/sites/landingpage/pintool/docs/98650/Pin/doc/html/group__INS__INSTRUMENTATION.html
    INS_InsertCall(ins, IPOINT_BEFORE, (AFUNPTR)docount, IARG_END);
}

// 当程序退出时调用此函数, 主要作用为关闭文件
VOID Fini(INT32 code, VOID* v)
{
    OutFile.setf(ios::showbase);
    OutFile << "Count " << icount << endl;
    OutFile.close();
}
// 使用方法
INT32 Usage()
{
    cerr << "This tool counts the number of dynamic instructions executed" << endl;
    cerr << endl << KNOB_BASE::StringKnobSummary() << endl;
    return -1;
}

int main(int argc, char* argv[])
{
    // 初始化 PIN, 如果初始化不成功则返回 Useage() 使用方法
    if (PIN_Init(argc, argv)) return Usage();
	// 打开全局输出文件
    OutFile.open(KnobOutputFile.Value().c_str());
    // 将 Instruction() 函数注册为指令插桩
    INS_AddInstrumentFunction(Instruction, 0);
    // 将 Fini() 函数注册为程序退出钩子
    PIN_AddFiniFunction(Fini, 0);
    // 启用目标程序, 并开始分析
    PIN_StartProgram();
    return 0;
}

```





































