# 网络和SSH

本文档整理了 Linux 系统中网络配置和SSH相关的命令。

## SSH 配置

### SSH 生成新的密钥

```shell
ssh-keygen -t rsa -b 4096
chmod 600 id_rsa
chmod 644 id_rsa.pub
```

## 网络配置

### Linux 添加虚拟网卡与配置网桥

```bash
#!/bin/sh
sudo modprobe tun tap
sudo ip link add br0 type bridge
sudo ip tuntap add dev tap0 mode tap
sudo ip tuntap add dev tap1 mode tap
sudo ip link set dev tap0 master br0
sudo ip link set dev tap1 master br0
sudo ip link set dev eno2 master br0
sudo ip link set dev br0 up

sudo ip address delete ************* dev eno2
sudo ip address add ************* dev br0
sudo ifconfig br0 ***************/24
sudo ifconfig tap0 up
sudo ifconfig tap1 up
sudo ip link set dev tap0 up
sudo ip link set dev tap1 up
```

### 查看网络连接

```shell
# 查看所有活动的网络连接
sudo ss -tunap
```

## 软件包管理

### Ubuntu 通过 PPA 安装高版本 NodeJS

```shell
curl -sL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get update
sudo apt-get install nodejs
node -v
```

## 相关链接

- [[系统配置]]
- [[进程和系统监控]]
- [[文件和目录操作]]
