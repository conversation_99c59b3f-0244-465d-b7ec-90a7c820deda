<GeminiOptimizationFrom>WAJI/mimalloc-benchmark.md</GeminiOptimizationFrom>
# Mimalloc Benckmark

---



> https://github.com/daanx/mimalloc-bench
>
> 



```bash
# pa 模块是损坏的无法编译, 解决方案: https://github.com/daanx/mimalloc-bench/issues/202
# 一定要加 sudo, 否则会报错
sudo ./build-bench-env.sh -r all no-pa


# 测试所有的样本以及所有的测试用例
​```bash
$ cd out/bench
$ ../../bench.sh alla allt    # all-allocator   all-test
​```
# 可以测试部分的测试用例
​```bash
$ cd out/bench
$ ../../bench.sh --procs=16 mi tc cfrac larson    # 测试 mimalloc 以及 tcmalloc 分配器, 测试 cfrac 以及 larson 测试集. 
​```
```



