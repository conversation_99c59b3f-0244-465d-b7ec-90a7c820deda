<GeminiOptimizationFrom>NixOS/Nix Flakes.md</GeminiOptimizationFrom>
# Nix Flakes 基本语法

---



### 1. 配置文件基本语法结构

在 **Git 项目**(flake 依赖 git)的根目录中, 创建一个名为 `flake.nix` 的文件, 对项目的开发环境, 运行环境, 以及编译过程进行定义. 确保项目在其他设备中也具有良好的可移植性.

`flake.nix` 的文件基本结构如下:

```nix
{
  description = "一个简单的 flake 示例";
  inputs = {
    # 依赖声明
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };
  outputs = { self, nixpkgs, ... }: {
    # 输出声明
    # ...
  };
}
```



#### 1.1 inputs

声明 flake 的依赖项, 例如:

```
inputs = {
  nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
  home-manager = {
    url = "github:nix-community/home-manager";
    inputs.nixpkgs.follows = "nixpkgs"; # 依赖跟随
  };
  flake-utils.url = "github:numtide/flake-utils";
};
```

Nix Flakes 支持多种 URL 格式：

1. **GitHub**：`github:owner/repo/ref`
2. **GitLab**：`gitlab:owner/repo/ref`
3. **本地路径**：`path:/absolute/path` 或 `path:./relative/path`
4. **Git**：`git+https://example.com/repo.git` 或 `git+ssh://***************/repo.git`
5. **Tarball**：`https://example.com/archive.tar.gz`
6. **Flake 注册表**：`flake:name`



#### 1.2 outputs

`outputs` 用于定义 flake 的输出. 其为一个函数, 接受 inputs 中定义的值, 并返回一套属性集合. 

```
outputs = { self, nixpkgs, ... }: {
  # 各种输出...
};
```

其中, `outputs` 函数的输入包含但不限于:

- self: 指向当前 flake 的特殊输入, 必须放在第一个位置, 包含了 flake 的所有输出以及元数据
- 其他在 inputs 中声明的依赖名称, 例如 nixpkgs



### 2. Nix Flakes 约定输出属性

| 约定输出属性 | 用途                                   | 典型结构          | 主要 Nix 命令交互方式                                        | 是否包含 `default` 属性                          |
| :----------- | :------------------------------------- | :---------------- | :----------------------------------------------------------- | :----------------------------------------------- |
| `packages`   | 定义可构建和安装的软件包               | `<system>.<name>` | `nix build .#<name>` `nix profile install .#<name>`          | 是 (`packages.<system>.default`)                 |
| `apps`       | 定义可直接运行的应用程序               | `<system>.<name>` | `nix run .#<name>`                                           | 是 (`apps.<system>.default`)                     |
| `devShells`  | 定义用于开发的隔离环境                 | `<system>.<name>` | `nix develop .#<name>`                                       | 是 (`devShells.<system>.default`)                |
| `lib`        | 导出可重用的 Nix 函数、属性集等库代码  | `<name>`          | 在其他 Nix 代码中通过 `self.lib` 引用                        | 通常不作为命令交互的默认，但可以有 `lib.default` |
| `overlays`   | 定义用于修改或扩展 Nixpkgs 的 overlays | `<name>`          | 在导入 `nixpkgs` 时应用 (`import nixpkgs { overlays = [ self.overlays.myOverlay ]; }`) | 是 (`overlays.default`)                          |
| `templates`  | 定义用于快速创建新项目的模板           | `<name>`          | `nix flake init -t .#<name>`                                 | 是 (`templates.default`)                         |

**说明：**

- `<system>` 通常是 `x86_64-linux`、`aarch64-darwin` 等系统架构标识符。
- `<name>` 是为包、应用、开发环境等定义的具体名称。
- 带有 `default` 属性的输出，在不指定 `<name>` 时会被相应的 Nix 命令默认使用（例如 `nix build .` 等同于 `nix build .#default`）。



### 3. 一个精心调整的模板

```nix
{
  description = "myapp -- Nix Flakes Template";

  inputs = {
    nixpkgs.url = "github:nixos/nixpkgs/release-22.11";
    flake-utils.url = "github:numtide/flake-utils";
    self.submodules = true;
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pname = "myapp";
        version = "0.0.1";

        pkgs = import nixpkgs {
          inherit system;
          config = {
            allowUnfree = false;
            permittedInsecurePackages = [
              "python-********"
            ];
          };
        };

        buildTools = with pkgs; [
          gcc
          bash
          gnumake
          autoconf
          pkg-config
        ];

        debugTools = with pkgs; [
          gdb
          git
          bear
          strace
        ];

        pythonEnv = pkgs.python39.withPackages (ps: with ps; [
          pip
          numpy
          pillow
        ]);

        coreDeps = with pkgs; [
          zlib
          glib
          libxml2
        ];
      in
      {
        # `nix build`; `nix build .`;  `nix build.#default`;  `nix build . -L`;
        packages.default = pkgs.stdenv.mkDerivation {
          name = "${pname}-${version}";
          src = ./.;

          # https://nixos.org/manual/nixpkgs/stable/#ssec-stdenv-dependencies-overview
          # 若依赖项在构建阶段执行，则应添加至 `nativeBuildInputs`. (build platform tools)
          #     - 主要是构造期间, 需要在 $PATH 中调用的程序 (如 cmake, pkg-config 等)
          nativeBuildInputs = [ pkgs.breakpointHook ] ++ buildTools ++ [ pythonEnv ];
          # 若依赖项会被复制/链接到最终输出或在运行时使用，则应添加至 `buildInputs`. (host tools)
          #     - 例如编译器调用的库(如 zlib), 以及 patchShebangs 以来的解释器 (perl)
          buildInputs = coreDeps;

          patchPhase = ''
            patchShebangs .
          '';
          configurePhase = ''
            ./configure --prefix=$out
          '';
          buildPhase = ''
            make V=1 -j
          '';
          installPhase = ''
            make install
          '';

          meta = with pkgs.lib; {
            description = "My awesome application";
            homepage = "https://example.com/myapp";
            license = licenses.mit;
            maintainers = [ maintainers.tylzh97 ];
            platforms = platforms.linux;
          };
        };

        # nix develop
        devShells.default = pkgs.mkShell {
          packages = debugTools
            ++ self.packages.${system}.default.nativeBuildInputs
            ++ self.packages.${system}.default.buildInputs;

          # 导出变量, 定义 Shell 的启动前命令
          shellHook = ''
            NIX_PATH_ONLY=$(echo $PATH | tr ':' '\n' | grep -E '/nix/store' | tr '\n' ':')
            export PATH=$NIX_PATH_ONLY
            export PATH=$PATH:${pkgs.coreutils}/bin:${pkgs.findutils}/bin:${pkgs.gnugrep}/bin
            PS1='\n\[\033[1;32m\][nix-shell:\w]\[\033[0m\]\n\$ '
          '';
        };
      }
    );
}
```



### 4. 在 Build 的时候使用网络

在 pure 模式下, `nix build` 会在一个没有网络的构建环境中运行. 如果构建系统中包含了访问网络的相关行为, 都会导致构建过程中断. 一个推荐的方法是, 通过 `pkgs.fetchurl` 提前指定需要下载的文件. 一个示例是:

```nix
{
  description = "...";

  inputs = {
    ...  
  };

  outputs = { ... }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        qemuSrc = pkgs.fetchurl {
          url = "https://download.qemu.org/qemu-2.10.0.tar.xz";
          sha256 = "55d81ac987a4821d2744359c026d766459319ba9c013746570369068d93ff335";
        };
        ...
      in rec {
        packages.default = pkgs.stdenv.mkDerivation {
          ...
          preConfigure = ''
            ln -s ${qemuSrc} $PWD/qemu_mode/qemu-2.10.0.tar.xz
          '';
          ...
        }
      }
    );
}

```



### 5. 调试 Nix Build 的过程

当 `nix build` 的过程失败时, `pkgs.breakpointHook` 包能够阻塞构建环境, 并且允许用户进入构建容器中进行调试. 一个示例的错误输出如下:

```log
...
app> build failed in configurePhase with exit code 255
app> To attach install cntr and run the following command as root:
app> 
app>    cntr attach -t command cntr-/nix/store/w7xyh195pizkyy44nr6pq387y2qyix1f-app-0.0.1
```



此时, 我们在另一个 Shell 环境中, 在 Root 用户中运行它输出的命令. root 中需要安装 `cntr` 工具(例如 `nix profile install nixpkgs#cntr`):

```
(root)$ cntr attach -t command cntr-/nix/store/w7xyh195pizkyy44nr6pq387y2qyix1f-app-0.0.1
```



当我们进入到 `nixbld@localhost` 构建容器环境中后, 运行`cntr exec bash`以启动真实的构建环境的命令行:

```
nixbld@localhost:/var/lib/cntr$ cntr exec bash
```

























