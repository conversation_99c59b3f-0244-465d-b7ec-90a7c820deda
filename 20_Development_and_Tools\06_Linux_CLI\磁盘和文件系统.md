# 磁盘和文件系统

本文档整理了 Linux 系统中磁盘管理和文件系统相关的命令。

## 内存文件系统

### 将一片内存映射为硬盘

要创建一个RAM磁盘，您可以使用以下命令：

```shell
mkdir /mnt/ramdisk
mount -t tmpfs -o size=256M tmpfs /mnt/ramdisk
# 取消挂载
sudo umount /mnt/ramdisk
```

在上述命令中，第一个命令创建一个名为"ramdisk"的目录，用于将RAM磁盘挂载。第二个命令创建了一个256MB的RAM磁盘，具有tmpfs文件系统，并将其挂载到/mnt/ramdisk目录中。

请注意，RAM磁盘通常只在重新启动后才会消失。

### 自动挂载临时文件系统

```shell
# 1. 编辑 /etc/fstab 配置
vi /etc/fstab
# 添加以下内容:
# tmpfs                                     /mnt/ramdisk    tmpfs   defaults,size=5G  0     0

# 2. 创建目录, 并确保目录本身不可被修改
mkdir -p ramdisk
chmod 777 /mnt/ramdisk/
chattr +i /mnt/ramdisk/

# 3. 挂载目录, 并查看是否成功挂载
mount -a
df -h
# tmpfs           5.0G     0  5.0G   0% /mnt/ramdisk
```

## 虚拟内存管理

### 创建虚拟内存 Swap Space

```shell
# https://docs.oracle.com/cd/E24457_01/html/E21988/giprn.html
# 创建一个存放 swapfile 的目录
sudo mkdir /swapfile && cd /swapfile/
# 在选定的目录中创建 Swap Space, 新的交换区大小为 30G
sudo dd if=/dev/zero of=<swapfile> bs=1024 count=30000000
# 初始化指定的 Swap Space
sudo mkswap -f <swapfile>
# 使用 swapon 启用新的 Swap Space
sudo swapon <swapfile>
# 查看交换空间是否添加成功
swapon -s

# 取消挂载 Swap Space
swapoff <swapfile>
```

## 磁盘扩容

### Linux 磁盘扩容

1. 确保虚拟机的状态不是**链接**而是完整的镜像
2. 在虚拟机中扩展磁盘空间，参考如下：

![磁盘扩容1](https://qiniu.maikebuke.com/202301091733664.png)

![磁盘扩容2](https://qiniu.maikebuke.com/202301091735792.png)

3. 相关命令

```bash
# 查看系统分区情况
$ df -h
# 查看具体的磁盘情况
$ sudo fdisk -l
# 对物理磁盘 /dev/sda 进行分区
$ sudo parted /dev/sda
    (parted) p
    # 此处填写上面输出的编号
    (parted) resizepart <id>
    # 标准输入 "yes"
    (parted) q
# 这里需要填写`fdisk -l`命令中查看到的 Linux File System sda编号
$ sudo resize2fs /dev/<sda_id>
# 再次查看分区容量
$ df -h
```

## 挂载管理

### 卸载挂载的软盘

如果开机出现 `blk_update_request: I/O error, dev df0, sector 0` 类似的提示，是由于 `/dev/fd0` 挂载了不存在的软盘导致的，可以编辑 `/etc/fstab` 文件，将其中的加载软盘的代码注释。

## 相关链接

- [[文件和目录操作]]
- [[系统配置]]
- [[进程和系统监控]]
