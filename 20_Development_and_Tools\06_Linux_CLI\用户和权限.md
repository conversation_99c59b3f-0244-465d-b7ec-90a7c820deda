# 用户和权限

本文档整理了 Linux 系统中用户管理和权限相关的命令。

## 用户管理

### 创建新用户

在Linux上创建新用户，需要使用以下命令：

#### 方法一：使用 useradd

1. **useradd**：用于添加新用户，并创建用户主目录和默认的系统文件。
   ```bash
   sudo useradd username
   ```

2. **passwd**：用于为用户设置密码。
   ```bash
   sudo passwd username
   ```

3. **usermod**：用于修改用户信息，如用户组、主目录等。
   ```bash
   sudo usermod -aG groupname username  # 将用户添加到指定用户组
   ```

4. **id**：用于查看用户的UID和GID。
   ```bash
   id username
   ```

5. **chown**：用于改变文件或目录权限
   ```bash
   chown <user>:<group> /home/<USER>
   ```

#### 方法二：使用 adduser

**adduser**：是一个更友好的用户创建命令，它会提供交互式的方式设置用户信息和密码。
```bash
sudo adduser username
```

> **注意**：创建用户需要root权限才能执行。

### 查看管理员

```shell
getent group sudo
```

### Ubuntu 忘记管理员密码强制修改

```shell
# Step 1: 进入 GRUB 引导, 并且选择 `recovery mode` 启动系统, 在 Vmware 中即为启动时按住 F2
```

![Recovery Mode](https://qiniu.maikebuke.com/20231023155910.png)

```shell
# Step 2: 进入系统后, 在 `recovery menu` 中选择 `root`, 进入管理员模式
```

![Root Mode](https://qiniu.maikebuke.com/20231023155947.png)

```shell
# Step 3: `recovery menu` 默认是 read-only 模式, 重新挂载磁盘为 read-write 模式 (这一步系统会卡死几秒钟)
mount -o remount,rw /
# Step 4: 使用 passwd 命令修改用户的密码
passwd ubuntu
```

## 64位系统安装32位包

```shell
sudo dpkg --add-architecture i386
sudo apt-get update
# sudo apt-get install libgnutls28-dev:i386
# sudo apt-get install gcc-multilib g++-multilib
sudo apt-get install <包名称>:i386
```

## 相关链接

- [[进程和系统监控]]
- [[系统配置]]
- [[文件和目录操作]]
