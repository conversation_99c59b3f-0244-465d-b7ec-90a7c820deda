# Makefile 指南

本文档介绍 Makefile 的基本语法和使用方法，帮助理解和编写高效的构建脚本。

## 基本概念

Makefile 是一个自动化构建工具的配置文件，用于定义如何编译和链接程序。它通过规则（rules）来描述文件之间的依赖关系和构建命令。

## 伪目标 `.PHONY`

在 Makefile 中，源和目标通常都是对应的输入文件与输出文件。当我们使用 `make all` 之类的命令编译伪目标 `all` 时，如果目录中存在相关文件名，可能会出现奇怪的错误。

为了避免这种错误，使用 **`.PHONY` 伪目标来表示相关的命令为命令而不是文件**，如 `.PHONY: all clean`。

```makefile
.PHONY: clean
clean:
    rm -rf *.o *.d
```

## 变量定义

变量用来保存编译过程中需要用到的值，如编译器选项、目标文件名等。

**语法**：`VARNAME = value`，其中 `VARNAME` 是变量名，`value` 是变量的值。

```makefile
CC = gcc
CFLAGS = -Wall -O2
OBJ = main.o foo.o bar.o
EXEC = myprog
```

## 规则定义

规则用来描述如何将源代码文件编译成目标文件。

**语法**：`target: prerequisites`
- `target`：目标文件名
- `prerequisites`：依赖文件名

**重要**：规则的下一行是执行编译操作的命令，必须使用 **Tab 缩进而不能使用空格缩进**。

```makefile
$(EXEC): $(OBJ)
    $(CC) $(CFLAGS) $^ -o $@

main.o: main.c
    $(CC) $(CFLAGS) -c $< -o $@

foo.o: foo.c
    $(CC) $(CFLAGS) -c $< -o $@

bar.o: bar.c
    $(CC) $(CFLAGS) -c $< -o $@
```

## 通配符规则

通配符规则用来描述如何将一类文件编译成目标文件。

**语法**：`%.o: %.c`，其中 `%` 表示通配符，可以匹配任意字符。

```makefile
%.o: %.c
    $(CC) $(CFLAGS) -c $< -o $@
```

## 函数和条件语句

Makefile 支持条件语句和函数，用来根据不同的条件执行不同的命令。

**语法**：`$(function arguments)` 和 `ifdef/ifeq`

```makefile
ifdef DEBUG
CFLAGS += -g
endif

ifeq ($(UNAME), Linux)
LIBS = -lpthread
endif

OBJ := $(patsubst %.c,%.o,$(wildcard *.c))
```

## 变量引用

Makefile 提供了多种变量引用方式：

1. `$(var)`：引用已定义的变量 var 的值
2. `$@`：表示规则中的目标文件名（Target Name）
3. `$<`：表示规则中的第一个依赖文件名（First Prerequisite）
4. `$^`：表示规则中所有的依赖文件名，以空格分隔（Prerequisites）
5. `$*`：表示规则中的目标文件名，但不包含后缀名（Target Name without Suffix）
6. `$(shell command)`：执行 shell 命令，并返回输出结果


## Makefile 模板

以下是一个完整的 Makefile 模板，展示了常用的构建模式：

```makefile
CC = gcc
CFLAGS = -Wall -O2
OBJ = main.o foo.o bar.o
EXEC = myprog

all: $(EXEC)

$(EXEC): $(OBJ)
    $(CC) $(CFLAGS) $^ -o $@

%.o: %.c
    $(CC) $(CFLAGS) -c $< -o $@

clean:
    rm -f $(OBJ) $(EXEC)

.PHONY: all clean
```

### 模板说明

这个 Makefile 包含以下组件：

- **变量定义**：`CC`、`CFLAGS`、`OBJ` 和 `EXEC` 分别表示编译器、编译选项、目标文件列表和可执行文件名
- **通配符规则**：用来编译所有的 `.c` 文件，生成对应的 `.o` 文件
- **链接规则**：将所有目标文件链接生成可执行文件
- **清理规则**：用来清除所有的目标文件和可执行文件
- **伪目标声明**：使用 `.PHONY` 说明 `all` 和 `clean` 是伪目标

### 使用方法

- `make` 或 `make all`：编译整个程序
- `make clean`：清除所有生成的文件

### 工作原理

当运行 `make` 命令时，Makefile 将会被解析并执行相应的命令：

1. 如果某个目标文件不存在或者其依赖文件比它新，就会执行相应的命令
2. 如果目标文件已经存在，并且它的依赖文件没有变化，就不会执行命令

这种增量构建机制大大提高了大型项目的编译效率。

## 总结

Makefile 是一个非常灵活和强大的工具，可以用来自动化编译过程，提高开发效率和代码质量。掌握 Makefile 的基本语法和使用方法是非常重要的，特别是在大型项目开发中。

## 相关链接

- [[编译工具链]]
- [[GCC_命令参考]]
- [[Clang_命令参考]]



























