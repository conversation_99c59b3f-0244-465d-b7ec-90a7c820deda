<GeminiOptimizationFrom>Building/Makefile.md</GeminiOptimizationFrom>
# Makefile

---



### 伪目标 `.PHONY`

`Makefile`, 在表示编译规则时, 源和目标通常都是对应的输入文件与输出文件. 当我们使用`make all`之类的命令编译伪目标`all`时, 如果目录中存在相关文件名, 可能会出奇奇怪怪的错误. 为了避免这种错误, 使用**`.PHONY`伪目标来表示相关的命令为命令而不是文件**, 如`.PHONY: all clean`;

```makefile
.PHONY: clean
clean:
    rm -rf *.o *.d
```



### 变量定义

用来保存编译过程中需要用到的值，如编译器选项、目标文件名等。语法为 VARNAME = value，其中 VARNAME 是变量名，value 是变量的值。示例：

```makefile
CC = gcc
CFLAGS = -Wall -O2
OBJ = main.o foo.o bar.o
EXEC = myprog
```



### 规则定义

用来描述如何将源代码文件编译成目标文件。语法为 `target: prerequisites`，其中 target 是目标文件名，prerequisites 是依赖文件名。规则的下一行是执行编译操作的命令，使用 **Tab 缩进而不能使用空格缩进**。示例：

```makefile
$(EXEC): $(OBJ)
    $(CC) $(CFLAGS) $^ -o $@

main.o: main.c
    $(CC) $(CFLAGS) -c $< -o $@

foo.o: foo.c
    $(CC) $(CFLAGS) -c $< -o $@

bar.o: bar.c
    $(CC) $(CFLAGS) -c $< -o $@
```



### 通配符规则

用来描述如何将一类文件编译成目标文件。语法为 `%.o: %.c`，其中 % 表示通配符，可以匹配任意字符。示例：

```makefile
%.o: %.c
    $(CC) $(CFLAGS) -c $< -o $@
```



### 函数和条件语句

用来根据不同的条件执行不同的命令。语法为 `$(function arguments)` 和 `ifdef/ifeq`。示例：

```makefile
ifdef DEBUG
CFLAGS += -g
endif

ifeq ($(UNAME), Linux)
LIBS = -lpthread
endif

OBJ := $(patsubst %.c,%.o,$(wildcard *.c))
```



### 变量引用

1. $(var)：引用已定义的变量 var 的值。
2. $@：表示规则中的目标文件名（Target Name）。
3. $<：表示规则中的第一个依赖文件名（First Prerequisite）。
4. $^：表示规则中所有的依赖文件名，以空格分隔（Prerequisites）。
5. $*：表示规则中的目标文件名，但不包含后缀名（Target Name without Suffix）。
6. $(shell command)：执行 shell 命令，并返回输出结果。



### Makefile 模板

这个 `Makefile` 定义了四个变量 CC、CFLAGS、OBJ 和 EXEC，分别表示编译器、编译选项、目标文件列表和可执行文件名。它包含了一个通配符规则，用来编译所有的 .c 文件，以生成对应的 .o 文件。它还定义了一个规则，用来链接所有的目标文件，生成可执行文件。最后，它还定义了一个 clean 规则，用来清除所有的目标文件和可执行文件。这个 `Makefile` 还使用了 .PHONY 来说明 all 和 clean 是伪目标（.PHONY），它们不表示文件，而是表示一个命令。all 表示编译整个程序，clean 表示清除所有生成的文件。

这个 `Makefile` 的使用方法如下：

- make：编译整个程序。
- make clean：清除所有生成的文件。

当运行 `make` 命令时，`Makefile` 将会被解析并执行相应的命令。如果某个目标文件不存在或者其依赖文件比它新，那么就会执行相应的命令。如果目标文件已经存在，并且它的依赖文件没有变化，那么就不会执行命令。

`Makefile` 是一个非常灵活和强大的工具，可以用来自动化编译过程，提高开发效率和代码质量。掌握 `Makefile` 的基本语法和使用方法是非常重要的，特别是在大型项目开发中。

```makefile
CC = gcc
CFLAGS = -Wall -O2
OBJ = main.o foo.o bar.o
EXEC = myprog

all: $(EXEC)

$(EXEC): $(OBJ)
    $(CC) $(CFLAGS) $^ -o $@

%.o: %.c
    $(CC) $(CFLAGS) -c $< -o $@

clean:
    rm -f $(OBJ) $(EXEC)

.PHONY: all clean
```



























