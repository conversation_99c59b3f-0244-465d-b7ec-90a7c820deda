<GeminiOptimizationFrom>LLVM/LLVMPass-02_安装LLVM.md</GeminiOptimizationFrom>
# 安装 LLVM

---



> https://llvm.org/docs/GettingStarted.html

```shell
# git clone https://github.com/llvm/llvm-project.git
git clone https://gitee.com/mirrors/llvm-project
cp -r llvm-project llvm-13.0.1
cd llvm-13.0.1
git checkout llvmorg-13.0.1
mkdir build
cd build
cmake ../llvm -G "Unix Makefiles" -DCMAKE_BUILD_TYPE=Release -DLLVM_ENABLE_PROJECTS="clang;clang-tools-extra;lldb;compiler-rt;lld" -DLLVM_ENABLE_RUNTIMES="libcxx;libcxxabi" -DCMAKE_INSTALL_PREFIX=${HOME}/.local/llvm/13.0.1/
make -j99
make install
```







