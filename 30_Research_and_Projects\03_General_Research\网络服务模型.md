<GeminiOptimizationFrom>Research/网络服务模型.md</GeminiOptimizationFrom>
# C 网络服务模型

---



### 服务端

```c
fd = socket();
// 可选, 配置 socket 选项
setsockopt(fd, ...);
// 将 socket 文件描述符绑定到指定的地址
bind(fd, address);
// 将 socket 文件描述符设置为 "监听"(Pasive) 模式, 并设置最大连接数
listen(fd, num_of_max_client);
while(True) {
    // 从 socket 文件描述符中接受客户端连接, 获取 client_fd
    client_fd = accept(fd, &address);
    // 通过 client_fd 向远程端口发送数据
    send(client_fd, &data, length, 0);
    // 通过 client_fd 接受远程端口的数据, 此处可以使用 read 函数读取, 但不推荐
    recv(client_fd, &data, length, 0);
    // 主动关闭远程客户端连接
    close(client_fd);
}
```



### 客户端

```c
fd = socket();
// 通过 server_address 连接到远程服务器
connect(fd, server_address);
while(True) {
    // 发送数据
    send(fd, buffer, size, 0);
    // 接受数据
    recv(fd, buffer, size, 0);
}
close(fd);
```



### 常用函数表

| 函数       | 原型                                                         | 描述 |
| ---------- | ------------------------------------------------------------ | ---- |
| socket     | `int socket(int domain, int type, int protocol);`            |      |
| setsockopt | `int setsockopt(int sockfd, int level, int optname, const void *optval, socklen_t optlen);` |      |
| bind       | `int bind(int sockfd, const struct sockaddr *addr, socklen_t addrlen);` |      |
| listen     | `int listen(int sockfd, int backlog);`                       |      |
| accept     | `int accept(int sockfd, struct sockaddr *addr, socklen_t *addrlen);` |      |
| send       | `ssize_t send(int sockfd, const void *buf, size_t len, int flags);` |      |
| recv       | `ssize_t recv(int sockfd, void *buf, size_t len, int flags);` |      |
| connect    | `int connect(int sockfd, const struct sockaddr *addr, socklen_t addrlen);` |      |
| close      | `int close(int fd);`                                         |      |













