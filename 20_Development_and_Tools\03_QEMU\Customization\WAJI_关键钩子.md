<GeminiOptimizationFrom>QEMU/WAJI 关键 HOOK 点.md</GeminiOptimizationFrom>
WAJI/QEMU 关键 HOOK 点

---





### Linux-User Mode 加载 ELF 文件

```c
// linux-user/elfload.c
static void load_elf_image(const char *image_name, int image_fd,
                           struct image_info *info, char **pinterp_name,
                           char bprm_buf[BPRM_BUF_SIZE]);
/*
mmap_unlock();
// 此处可以显示文件的映射情况
close(image_fd);
*/
```





