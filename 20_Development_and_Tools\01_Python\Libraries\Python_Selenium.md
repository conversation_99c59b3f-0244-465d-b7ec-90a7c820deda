<GeminiOptimizationFrom>Python/Selenium.md</GeminiOptimizationFrom>
# Selenium Web 自动化测试框架

---



[Selenium 官方中文文档](https://www.selenium.dev/zh-cn/documentation/)



## 安装

**通过`pip`**

```bash
(bash) pip install selenium
```

**好用的打码库: `ddddocr`**

> https://github.com/sml2h3/ddddocr

```bash
(bash) pip install ddddocr
```

```python
import ddddocr
ocr = ddddocr.DdddOcr(show_ad=False)
with open("captcha.jpg", "rb") as f:
    data = f.read()
	captcha_texts = ocr.classification(data)
    print(captcha_texts)
```



**Selenium 依赖浏览器 Driver, 参考以下链接:**

[浏览器驱动快速参考](https://www.selenium.dev/documentation/webdriver/getting_started/install_drivers/#quick-reference)

[Chrome Drivers Page](https://chromedriver.chromium.org/downloads)



## API介绍



**常用引入库**

```python
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import WebDriverWait
from selenium.common.exceptions import TimeoutException
```

**创建一个 Chrome Webdriver**

```python
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

# 创建 Webdriver 选项
chrome_options = Options()
chrome_options.add_argument(" log-level=3")
chrome_options.add_argument('--ignore-certificate-errors')
# 选择 Driver 路径
service = Service(executable_path="driver/chromedriver.exe")
# 创建 Driver
driver = webdriver.Chrome(service=service, options=chrome_options)
```

**请求网页**

```python
URL = "https://www.baidu.com/"
driver.get(URL)
driver.implicitly_wait(1)
```

**查找网页中的元素**

```python
from selenium.webdriver.common.by import By

# 通过 By 提供的不同选择器, 选择网页中的元素
## {driver.find_element, driver.find_elements}
el_username = driver.find_element(by=By.CSS_SELECTOR, value="input[name=username]")
el_password = driver.find_element(by=By.CSS_SELECTOR, value="input[name=password]")
el_captchaT = driver.find_element(by=By.CSS_SELECTOR, value="input[name=code]")
el_captchaI = driver.find_element(by=By.ID, value="codeImg")
el_loginbtn = driver.find_element(by=By.ID, value="login-submit")
# 向指定的元素发送按键序列
el_username.send_keys(USERNAME)
el_password.send_keys(PASSWORD)
```

**截图获取浏览器中的元素**

```python
captcha_bytes = el_captcha.screenshot_as_png
```

**在页面中执行一段 JS 脚本**

```python
driver.execute_script('document.querySelector("[class=green]").target="_self"')
```

**获取浏览器当前保存的 Cookie**

```python
# ret: List[Dict]
driver.get_cookies()
```

