<GeminiOptimizationFrom>Python/WatchDog 文件系统监控.md</GeminiOptimizationFrom>
# Watchdog

---

[Watchdog Docs](https://pythonhosted.org/watchdog/)

[Watchdog Pypi](https://pypi.org/project/watchdog/)

[Watchdog Github](https://github.com/gorakhargosh/watchdog)

## 安装

```bash
(bash) pip install watchdog
```



## 引用

```python
from watchdog.observers import Observer
from watchdog.observers.api import ObservedWatch
from watchdog.events import LoggingEventHandler, FileSystemEventHandler, FileSystemEvent
```



## 使用

```python
import logging
from watchdog.observers import Observer
from watchdog.events import LoggingEventHandler, FileSystemEventHandler, FileSystemEvent

class MyEventHandler(FileSystemEventHandler):
    def __init__(self, base_dir: str, github_token: str) -> None:
        super().__init__()

    def on_any_event(self, event: FileSystemEvent):
        return super().on_any_event(event)

    def on_created(self, event: FileSystemEvent):
        return super().on_created(event)

    def on_deleted(self, event: FileSystemEvent):
        return super().on_deleted(event)

    def on_modified(self, event: FileSystemEvent):
        return super().on_modified(event)
    
    def on_moved(self, event: FileSystemEvent):
        return super().on_moved(event)

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')
path = sys.argv[1] if len(sys.argv) > 1 else '.'
# event_handler = LoggingEventHandler()
observer = Observer()
# observer.schedule(event_handler, path, recursive=True)
observer.schedule(MyEventHandler(), path, recursive=True)
observer.start()
try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    observer.stop()
observer.join()
```



