<GeminiOptimizationFrom>CTF-PWN/libc.md</GeminiOptimizationFrom>
# PWN Libc

---





> https://a1ex.online/2020/09/24/Docker%E4%B8%8BPWN%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/



## 不同ubuntu版本的docker

由于上面 patchelf 失败了，又经过同学 提醒可以直接安装有不同glibc版本的多个 ubuntu docker，所以又开始了直接搭建不同Ubuntu版本的docker。

下面是可以查看 ubuntu版本的 glibc版本的地址：

https://launchpad.net/glibc/+packages

在上面找到后：

```
ubuntu16.04    glibc2.23
ubuntu17.04	   glibc2.24
ubuntu17.10    glibc2.26
ubuntu18.04    glibc.2.27
```

然后就是拉去对应版本的docker，布置pwn环境了。



**注意：**ubuntu17.04 ubuntu17.10 ubuntu19.04 这些版本都不是 ubuntu官方长期支持的，所以使用源时：

```
sudo sed -i -re 's/([a-z]{2}\.)?archive.ubuntu.com|security.ubuntu.com/old-releases.ubuntu.com/g' /etc/apt/sources.list
```

得使用 old-release 版本。

不然用官方版本，就会报错，然后apt-get install 下载不了东西，这个是我踩的最深的坑了。



Docker内的环境：

pwndbg:

```
git clone https://github.com/pwndbg/pwndbg
cd pwndbg
./setup.sh
```

pwntools:

```
python3 -m pip install --upgrade pwntools
```

32位环境：

```
sudo dpkg --add-architecture i386
sudo apt install libc6:i386
```

其他工具：

```
python3 -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -U pip && \
    python3 -m pip config set global.index-url http://mirrors.aliyun.com/pypi/simple && \
    python3 -m pip config set install.trusted-host mirrors.aliyun.com && \
    python3 -m pip install --no-cache-dir --use-feature=2020-resolver\
    ropgadget \
    pwntools \
    z3-solver \
    smmap2 \
    apscheduler \
    ropper \
    unicorn \
    keystone-engine \
    capstone \
    angr \
    pebble \
    r2pipe \
    filebytes \
    keystone-engine
```





### 手动构建带调试信息的 glibc

> git clone https://sourceware.org/git/glibc.git
>
> mkdir glibc-build && cd glibc-build
>
> mkdir configure build && cd configure
>
> CC="gcc -O1 -g -ggdb" ../../glibc/configure --prefix=`realpath ../build`
>
> `# --disable-werror --enable-add-ons --without-selinux`
>
> make -j && make install













































