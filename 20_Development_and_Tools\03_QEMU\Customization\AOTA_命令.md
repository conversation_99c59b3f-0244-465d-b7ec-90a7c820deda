<GeminiOptimizationFrom>QEMU/AOTA 命令.md</GeminiOptimizationFrom>
#  AOTA 命令

---

## AOTA 安装

仓库地址：

- `https://192.168.3.202/AotaQemuFrame/AotaQemuUserPluginFrame`
- `git@192.168.3.202:AotaQemuFrame/AotaQemuUserPluginFrame.git`
- `https://github.com/tylzh97/AOTA-Qemu.git`

要求：glibc<2.30 [参考](https://github.com/ncbi/ncbi-vdb/issues/21)

```shell
cd ~ && mkdir AOTA && cd AOTA
sudo apt-get install curl
curl -O https://cdn.jsdelivr.net/gh/tylzh97/smallfiles/libversion.a
sudo mv libversion.a /usr/local/lib
sudo chmod 755 /usr/local/lib/libversion.a
sudo apt-get install -y build-essential ninja-build libglib2.0-dev libpixman-1-dev 
<NAME_EMAIL>:tylzh97/AOTA-Qemu.git
cd AOTA-Qemu
mkdir rhbin
./configure --target-list=i386-linux-user --extra-cflags="-Wno-error=missing-prototypes" --prefix=`pwd`/rhbin
make
make install
cp -r rhbin ~/aotabin
```



## AOTA 常用命令



QEMU 镜像配置网卡

```shell
ifconfig -a
sudo ifconfig ens3 10.0.2.15
route -n
sudo route add default gw 10.0.2.2
```



AOTA 制作快照

```shell
## 启动 QEMU
qemu-system-AOTA-x86_64 
	# 选择 QCOW 镜像
    -hda <QEMU Image>.qcow2 
    # 选择内存大小
    -m 2048 
    # 配置虚拟网卡
    -net nic,model=rtl8139 -net user 
    # 开启 QEMU 控制台
    -monitor stdio 
    # 使用 USB 设备, 与使用鼠标相关
    -usb -device usb-tablet 
    # 开启声卡
    -soundhw all
    # 开启快照模式, 此时不会修改 -hda 文件的内容
    -snapshot 
    # 开启 VNC 服务, 端口号为 5000+port, 此例为5025
    -vnc :25

## QEMU Monitor 中输入命令打包快照
migrate "exec: gzip -c > <snapshot_path>.gz"
```

AOTA 使用快照模式启动

```shell
qemu-system-AOTA-x86_64 
    -hda <QEMU Image>.qcow2 
    -m 2048 
    -net nic,model=rtl8139 -net user 
    -monitor stdio 
    -usb -device usb-tablet 
    -soundhw all 
    -snapshot 
    # 启动时加载快照
    -incoming "exec: gzip -c -d <snapshot_path>.gz" 
    # 加载 AOTA-System 插件
    -aotaplugin /home/<USER>/aotabin/aeav_plugin.so 
    # 加载 AOTA 配置文件
    -configfile /home/<USER>/config/win7sp1_vlc.json 
    # 通过光驱的方式插入虚拟光驱, 相当于导入外部文件
    -cdrom /home/<USER>/xxx.iso 
    -vnc :25
```



指定桥接物理网卡设备

```shell
/home/<USER>/AotaTools/qemu_trace_system/bin/aota-citrix-system-x86_64 \
    -hda /home/<USER>/lizhenghao/img/ubt1604_32.qcow2 \
    -m 2048 \
    -monitor stdio \
    -usb -device usb-tablet \
    -smp 2,sockets=2,cores=1,threads=1 \
    -net nic,model=virtio,macaddr=00:AA:11:22:33:44 -net tap,ifname=tap2,script=no,downscript=no \
    -snapshot \
    -vnc :25
```







```bash
qemu-system-i386 -m 3G -enable-kvm -hda ubuntu_1604_x86.qcow -net nic,model=e1000 -net user,hostfwd=tcp::12022-:22 -monitor stdio -vnc :25
```





### AOTA 配置文件



配置污点源: `condition_str` 表示污点文件名



