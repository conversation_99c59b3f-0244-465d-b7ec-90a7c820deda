<GeminiOptimizationFrom>Python/Bytecode.md</GeminiOptimizationFrom>
# Python Bytecode

---



### 1. 查看所有的字节码

```python
import dis
print("所有字节码操作符:", dis.opname)
print("操作码总数:", len(dis.opname))
# ['CACHE', 'POP_TOP', 'PUSH_NULL', '<3>', '<4>', '<5>', '<6>', ...]
```



```python
import dis

# 获取操作码名称列表
opname_list = dis.opname
print(f"操作码名称列表的长度: {len(opname_list)}")

# 获取操作码到字节码值的映射
opmap_dict = dis.opmap
print(f"实际操作码的数量: {len(opmap_dict)}")
# {'CACHE': 0, 'POP_TOP': 1, 'PUSH_NULL': 2, 'NOP': 9, ...}
```



### 2. 常见的字节码功能

#### **栈操作**

- `LOAD_CONST`：加载常量（如数字、字符串）
- `POP_TOP`：弹出栈顶元素
- `ROT_TWO`：交换栈顶两个元素

#### **变量操作**

- `LOAD_FAST`：加载局部变量
- `STORE_FAST`：存储局部变量
- `LOAD_GLOBAL`：加载全局变量

#### **算术运算**

- `BINARY_ADD`：加法 `+`
- `BINARY_MULTIPLY`：乘法 `*`
- `COMPARE_OP`：比较操作（如 `==`, `<`）

#### **控制流**

- `JUMP_FORWARD`：向前跳转
- `POP_JUMP_IF_FALSE`：条件跳转
- `SETUP_LOOP`：设置循环块

#### **函数调用**

- `CALL_FUNCTION`：调用函数
- `RETURN_VALUE`：返回值
- `MAKE_FUNCTION`：创建函数对象

#### **数据结构**

- `BUILD_LIST`：构建列表
- `BUILD_MAP`：构建字典
- `BUILD_TUPLE`：构建元组

#### **异步/协程**

- `GET_AWAITABLE`：获取可等待对象
- `YIELD_FROM`：从生成器产出值



### 3. 常见的字节码读取方式

- 获取某条 Bytecode Instruction 的具体信息	

    ```python
    import dis
    def repr_instr(i: dis.Instruction):
        s = (
            f"{str(i.starts_line) if i.starts_line else '': <{5}}"
            f"{'>>' if i.is_jump_target else '': >{4}}"
            f"{i.offset: >5d} {i.opname: <{16}} "
            f"[{i.arg}]->({i.argrepr}{type(i.argrepr)}, {i.argval}{type(i.argval)})"
        )
        return s
    ```

- 获取所有加载的变量 / 常量 / 全局变量

    ```python
    cache_lineno: int = 0
    for instr in dis.get_instructions(frame.f_code):
    	# 如同 dis.disassemble(code_obj) 显示的一样, 不是所有的 bytecode 都有 starts_line 属性
    	# 通过缓存上一个 starts_line 属性, 能够比较精准的获取当前行的所有字节码
        cache_lineno = instr.starts_line if instr.starts_line else cache_lineno
        if cache_lineno == lineno:
            if instr.opname.startswith('LOAD'):
                var_value = None
                if instr.opname == 'LOAD_FAST':
                    var_name = code_obj.co_varnames[instr.arg]
                    var_value = frame.f_locals.get(var_name)
    			elif instr.opname == 'LOAD_GLOBAL':
                    var_name = code_obj.co_names[instr.arg >> 1]
                    var_value = frame.f_globals.get(var_name)
                    if var_value is None: # 全局变量可能是内建函数, 此时需要
                        var_value = frame.f_builtins.get(var_name)
    			elif instr.opname == 'LOAD_CONST':
                    # 无视常量数据
                    continue
    
    			print(f"{repr_instr(instr)}")
    ```



### 4. 常见的语法的 Bytecode Pattern

- **比教语句(if)**

    ```
    136          12 LOAD_FAST                     2 (z)
    
    137          14 LOAD_CONST                    1 (10)
    
    136          16 COMPARE_OP               	  0 (<)
                 22 POP_JUMP_FORWARD_IF_FALSE     6 (to 36)
    ```

    [LOAD, LOAD, COMPARE, JUMP]

- **函数调用(call)**

    ```
    144     >>   62 LOAD_GLOBAL              1 (NULL + print)
                 74 LOAD_FAST                4 (i)
                 76 PRECALL                  1
                 80 CALL                     1
                 90 POP_TOP
    ```

    [LOAD[function], LOAD[args]*n, PRECALL[num_of_args], CALL[num_of_args], POP_TOP(弹出返回值)]

- **while循环**

    ```python
    143          50 LOAD_FAST                4 (i)
                 52 LOAD_CONST               5 (5)
                 54 COMPARE_OP               0 (<)
                 60 POP_JUMP_FORWARD_IF_FALSE    26 (to 114)
    
    144          92 LOAD_FAST                4 (i)
                 94 LOAD_CONST               6 (1)
                 96 BINARY_OP               13 (+=)
                100 STORE_FAST               4 (i)
    
    143         102 LOAD_FAST                4 (i)
                104 LOAD_CONST               5 (5)
                106 COMPARE_OP               0 (<)
                112 POP_JUMP_BACKWARD_IF_TRUE    26 (to 62)
    ```

    有点类似于 C 语言的 `do-while` 结构, 同一行会出现两次, 一个向前跳转, 一个向后跳转









### [附录]. 对照表

> https://docs.python.org/3/library/dis.html

```json
["NOP", "POP_TOP", "END_FOR", "END_SEND", "COPY", "SWAP", "CACHE", "UNARY_NEGATIVE", "UNARY_NOT", "UNARY_INVERT", "GET_ITER", "GET_YIELD_FROM_ITER", "TO_BOOL", "BINARY_OP", "BINARY_SUBSCR", "STORE_SUBSCR", "DELETE_SUBSCR", "BINARY_SLICE", "STORE_SLICE", "GET_AWAITABLE", "GET_AITER", "GET_ANEXT", "END_ASYNC_FOR", "CLEANUP_THROW", "BEFORE_ASYNC_WITH", "SET_ADD", "LIST_APPEND", "MAP_ADD", "RETURN_VALUE", "RETURN_CONST", "YIELD_VALUE", "SETUP_ANNOTATIONS", "POP_EXCEPT", "RERAISE", "PUSH_EXC_INFO", "CHECK_EXC_MATCH", "CHECK_EG_MATCH", "WITH_EXCEPT_START", "LOAD_ASSERTION_ERROR", "LOAD_BUILD_CLASS", "BEFORE_WITH", "GET_LEN", "MATCH_MAPPING", "MATCH_SEQUENCE", "MATCH_KEYS", "STORE_NAME", "DELETE_NAME", "UNPACK_SEQUENCE", "UNPACK_EX", "STORE_ATTR", "DELETE_ATTR", "STORE_GLOBAL", "DELETE_GLOBAL", "LOAD_CONST", "LOAD_NAME", "LOAD_LOCALS", "LOAD_FROM_DICT_OR_GLOBALS", "BUILD_TUPLE", "BUILD_LIST", "BUILD_SET", "BUILD_MAP", "BUILD_CONST_KEY_MAP", "BUILD_STRING", "LIST_EXTEND", "SET_UPDATE", "DICT_UPDATE", "DICT_MERGE", "LOAD_ATTR", "LOAD_SUPER_ATTR", "COMPARE_OP", "IS_OP", "CONTAINS_OP", "IMPORT_NAME", "IMPORT_FROM", "JUMP_FORWARD", "JUMP_BACKWARD", "JUMP_BACKWARD_NO_INTERRUPT", "POP_JUMP_IF_TRUE", "POP_JUMP_IF_FALSE", "POP_JUMP_IF_NOT_NONE", "POP_JUMP_IF_NONE", "FOR_ITER", "LOAD_GLOBAL", "LOAD_FAST", "LOAD_FAST_LOAD_FAST", "LOAD_FAST_CHECK", "LOAD_FAST_AND_CLEAR", "STORE_FAST", "STORE_FAST_STORE_FAST", "STORE_FAST_LOAD_FAST", "DELETE_FAST", "MAKE_CELL", "LOAD_DEREF", "LOAD_FROM_DICT_OR_DEREF", "STORE_DEREF", "DELETE_DEREF", "COPY_FREE_VARS", "RAISE_VARARGS", "CALL", "CALL_KW", "CALL_FUNCTION_EX", "PUSH_NULL", "MAKE_FUNCTION", "SET_FUNCTION_ATTRIBUTE", "BUILD_SLICE", "EXTENDED_ARG", "CONVERT_VALUE", "FORMAT_SIMPLE", "FORMAT_WITH_SPEC", "MATCH_CLASS", "RESUME", "RETURN_GENERATOR", "SEND", "HAVE_ARGUMENT", "CALL_INTRINSIC_1", "CALL_INTRINSIC_2", "SETUP_FINALLY", "SETUP_CLEANUP", "SETUP_WITH", "POP_BLOCK", "JUMP", "JUMP_NO_INTERRUPT", "LOAD_CLOSURE", "LOAD_METHOD"]
```

























