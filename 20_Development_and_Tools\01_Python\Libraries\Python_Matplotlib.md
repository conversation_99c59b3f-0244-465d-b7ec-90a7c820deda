<GeminiOptimizationFrom>Python/Matplotlib.md</GeminiOptimizationFrom>
# Matplotlib 使用说明

---



[matplotlib]: https://matplotlib.org/stable/api/	"Matplotlib API参考手册"



## 安装与导入

**安装**

```bash
(bash) pip install matplotlib
```

**导入**

```python3
>>> import matplotlib.pyplot as plt
```



## 通用

```python
# 导入库
import matplotlib.pyplot as plt
# 设置图片样式
# https://matplotlib.org/stable/gallery/style_sheets/style_sheets_reference.html
plt.style.use('ggplot')
# 设置图像属性
# https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.figure.html
plt.figure(num=1, figsize=(16, 9), dpi=100, facecolor="w")
# 添加文字
plt.text(1, 1, "Hello World")

# 在窗口中显示图片
plt.show()
# 保存图片
plt.savefig("plt.png")
```



## 绘制包含多张子图的 Figure

```python
import numpy as np
import matplotlib.pyplot as plt

# 配置 Figure 中的子图数
# 这个博客不错: https://blog.51cto.com/u_15671528/5526422
# https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.subplots.html
fig, ax = plt.subplots(nrows=2, ncols=3)
# 绘制一个折线图, 放在 (0, 0) 位置
x=np.arange(1,100)
ax[0][0].plot(x, x*x)
# 绘制一个散点图, 放在 (0, 1) 位置
ax[0][1].scatter(np.arange(0,10), np.random.rand(10))
# 绘制一个饼图, 放在 (1, 0) 位置
ax[1][0].pie(x=[15,30,45,10],labels=list('ABCD'),autopct='%.0f',explode=[0,0.05,0,0])
# 绘制一个条形图, 放在 (1, 1) 位置
ax[1][1].bar([20,10,30,25,15],[25,15,35,30,20],color='b')
plt.show()

# Q: 如何设置输出图像的大小?
# A: fig.set_size_inches(10, 8) # 宽度, 长度
# Q: 如何设置整体图片的title?
# A: fig.suptitle("Big Title")
# Q: 如何显示子图的title?
# A: ax[0][0].set_title("Small Title")
# Q: 如何关闭子图坐标轴?
# A: ax.axis("off")
```

<img src="https://qiniu.maikebuke.com/202212251217016.png" style="zoom:67%;" />



## 设置坐标轴

```python
# https://matplotlib.org/stable/api/_as_gen/matplotlib.pyplot.gca.html
i = plt.gca()
i.set_xticks(x)
i.set_xticklabels(x, rotation=45, ha='center')
```



## 柱状图

**普通柱状图**

```python
import numpy as np
import matplotlib.pyplot as plt

# 设置参数, 分别是步长与柱状图的柱宽
PI = math.pi
STEP = 0.1
BAR_WIDTH = 0.8 * STEP

# 输入
## 柱状图显示的横坐标 x 数组, 要求类型 dtype=int|float
x = np.arange(-PI, PI, STEP)
## 柱状图显示的高度值 y 数组, 要求类型 dtype=int|float
y = np.sin(x)

# 调用 plt.bar 方法, 绘制柱状图
plt.bar(x=x, height=y, width=BAR_WIDTH, alpha=0.6, label=x)
plt.show()
```

<img src="https://qiniu.maikebuke.com/202212250948444.png" alt="普通柱状图" style="zoom:67%;" />

**分组柱状图**

计算每组的显示横坐标`x`, 与宽度`width`,然后调用`plt.bar`显示即可

```python
import numpy as np
import matplotlib.pyplot as plt

x = np.array([2008, 2009, 2010, 2011, 2012])
y_bj = np.array([156, 189, 273, 534, 99])
y_sh = np.array([183, 266, 142, 503, 266])

width = 0.4
half_w = width/2

# 第一类数据的 bar
## x 坐标需要计算一下
plt_bj = plt.bar(x=x-half_w, height=y_bj, width=width, label="Beijing")
## 显示具体数值, label 表示具体的显示位置 {"edge", "center"}
plt.bar_label(plt_bj, label_type="edge")
# 第二类数据的 bar
plt_sh = plt.bar(x=x+half_w, height=y_sh, width=width, label="Shanghai")
plt.bar_label(plt_sh, label_type="center")

# 显示 title
plt.title('GDP about Beijing and Shanghai')
# 显示图例, 即设置的 label
plt.legend()
plt.show()
```



<img src="https://qiniu.maikebuke.com/202212251008860.png" alt="分组柱状图" style="zoom: 67%;" />

**堆积柱状图**

```python
import numpy as np
import matplotlib.pyplot as plt

x = np.arange(5)
x_label = np.array(["Apple", "Google", "Telsa", "Twitter", "META"])
y_total  = np.array([572, 486, 273, 358, 99])
y_growth = np.array([82, 42, 64, 33, -30])

width = 0.8
# 设置柱状图
plt_t = plt.bar(x=x, height=y_total, width=width, alpha=0.5, label="Total")
plt.bar_label(plt_t, label_type="center")
## 设置当前 bar 的起始坐标 bottom 为上一个柱状图的 height
plt_g = plt.bar(x=x, height=y_growth, width=width, bottom=y_total, alpha=0.5, label="Growth")
plt.bar_label(plt_g, label_type="center")

# 坐标轴设置
i = plt.gca()
## 设置横坐标轴
i.set_xticks(x)
i.set_xticklabels(x_label, rotation=45, ha='center')

plt.title('Technology Company Financials')
plt.legend()
plt.show()
```

<img src="https://qiniu.maikebuke.com/202212251030486.png" alt="堆积柱状图" style="zoom:67%;" />

## 折线图

**普通折线图**

```python
import numpy as np
import matplotlib.pyplot as plt

# 选择一个样式, 这个还挺好看的
plt.style.use('ggplot')
# 设置图像属性
plt.figure(num=1, figsize=(16, 9), dpi=100, facecolor="w")

# 生成数据
x = np.arange(10)
y = x**2 + 1

# 绘制折线图
plt.plot(x, y, "o", linestyle='-')

# 在图片上添加一个文字
plt.text(x[0], y[0], "Hello World")
plt.show()
```

<img src="https://qiniu.maikebuke.com/202212251156782.png" alt="普通折线图" style="zoom:50%;" />









