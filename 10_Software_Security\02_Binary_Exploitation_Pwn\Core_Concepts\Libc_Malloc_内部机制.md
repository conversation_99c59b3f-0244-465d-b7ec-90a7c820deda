<GeminiOptimizationFrom>CTF-PWN/libc-malloc.md</GeminiOptimizationFrom>
# libc malloc

---









> libc 官方 git: https://sourceware.org/git/gitweb.cgi?p=glibc.git 
>
> https://sourceware.org/git/glibc.git
>
> libc github mirror: https://github.com/bminor/glibc
>
> glibc wiki: https://sourceware.org/glibc/wiki/MallocInternals



备忘

> `_int_`开头的函数表示 `internal`, 这些是内部实现的函数, 不会暴露到外部



```c
/*
  This struct declaration is misleading (but accurate and necessary).
  It declares a "view" into memory allowing access to necessary
  fields at known offsets from a given base. See explanation below.
*/

struct malloc_chunk {

  INTERNAL_SIZE_T      mchunk_prev_size;  /* Size of previous chunk (if free).  */
  INTERNAL_SIZE_T      mchunk_size;       /* Size in bytes, including overhead. */

  struct malloc_chunk* fd;         /* double links -- used only if free. */
  struct malloc_chunk* bk;

  /* Only used for large blocks: pointer to next larger size.  */
  struct malloc_chunk* fd_nextsize; /* double links -- used only if free. */
  struct malloc_chunk* bk_nextsize;
};


typedef struct malloc_chunk *mbinptr;
```



**main_arena**

```c
static struct malloc_state main_arena =
{
  .mutex = _LIBC_LOCK_INITIALIZER,
  .next = &main_arena,
  .attached_threads = 1
};
```





**Fast Bins**

```c
typedef struct malloc_chunk *mfastbinptr;
#define fastbin(ar_ptr, idx) ((ar_ptr)->fastbinsY[idx])

/* offset 2 to use otherwise unindexable first 2 bins */
#define fastbin_index(sz) \
  ((((unsigned int) (sz)) >> (SIZE_SZ == 8 ? 4 : 3)) - 2)


/* The maximum fastbin request size we support */
#define MAX_FAST_SIZE     (80 * SIZE_SZ / 4)

#define NFASTBINS  (fastbin_index (request2size (MAX_FAST_SIZE)) + 1)
```



### PT Memory Allocation

|  变量/宏/函数  |  描述 |   示例   |
| ---- | ---- | ---- |
| victim | **inspected/selected chunk**, 被选择的 Chunk | victim = av->top; |
| main_arena | 主分配区域. glibc 的 malloc.c 中存在一个静态变量唯一指向主分配区域, 通过 next 指针指向其他的分配区域. | static struct malloc_state main_arena; |
| ar_ptr | 一个指向`malloc_state` 的指针, 可以用来遍历所有的 `Arenas`. 可以通过 `arena.c:_int_new_arena` 创建一个新的 `Arena` | mstate ar_ptr = &main_arena;           |
| av | **arena vector**, 通常作为函数的**形式参数**使用, 表示当前的堆操作行为所在的分配区域 | struct malloc_state * av; |
|  |  |  |
|      |      |      |





























