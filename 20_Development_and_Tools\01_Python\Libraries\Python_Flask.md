<GeminiOptimizationFrom>Python/Flask.md</GeminiOptimizationFrom>
# Flask 框架

---



```shell
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple ddddocr==1.4.7 flask==2.1.0 flask-cors==3.0.10
```





```python

import os
import re
import sys
import ddddocr
from base64 import b64decode
from flask_cors import cross_origin
from flask import Flask, request, Response

ocr = ddddocr.DdddOcr(show_ad=False)

app = Flask(__name__)

@app.before_request
def flask_before_request():
    headers = dict(request.headers)
    print(headers)
    print("Flask Before Request Hook")

@app.after_request
def flask_after_request(response: Response):
    print("Flask After Request Hook")
    print(type(response))
    sys.stdout.flush()
    return response

@app.route("/", methods=["GET"])
def index():
    return "Hello World!"

JS_BASE64_PATTERN = re.compile(r"^data:[\w\d]+/[\w\d]+;base64,(.+)$")
@app.route("/captcha", methods=["POST"])
@cross_origin()
def handle_captcha():
    # 检查域名是否来自ucas
    assert "ucas.ac.cn" in request.headers.get("Origin")
    # 获取数据
    data = request.get_json()
    assert isinstance(data, dict)
    image_base64 = data.get("image")
    assert re.match(JS_BASE64_PATTERN, image_base64)
    image_base64 = re.findall(JS_BASE64_PATTERN, image_base64)[0]
    image = b64decode(image_base64)
    # ddddocr分类
    captcha_texts = ocr.classification(image)
    resp = {"code": 0, "data": captcha_texts}
    return resp

if __name__ == "__main__":
    app.run("0.0.0.0", "8000", debug=True)

```



