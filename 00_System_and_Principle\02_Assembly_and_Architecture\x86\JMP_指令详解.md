<GeminiOptimizationFrom>AssemblyCode/JMP.md</GeminiOptimizationFrom>
# JMP

---





> https://www.felixcloutier.com/x86/jmp







| Opcode      | Instruction  | Op/En | 64-Bit Mode | Compat/Leg Mode | Description                                                  |
| ----------- | ------------ | ----- | ----------- | --------------- | ------------------------------------------------------------ |
| EB cb       | JMP rel8     | D     | Valid       | Valid           | Jump short, RIP = RIP + 8-bit displacement sign extended to 64-bits. |
| E9 cw       | JMP rel16    | D     | N.S.        | Valid           | Jump near, relative, displacement relative to next instruction. Not supported in 64-bit mode. |
| E9 cd       | JMP rel32    | D     | Valid       | Valid           | Jump near, relative, RIP = RIP + 32-bit displacement sign extended to 64-bits. |
| FF /4       | JMP r/m16    | M     | N.S.        | Valid           | Jump near, absolute indirect, address = zero-extended r/m16. Not supported in 64-bit mode. |
| FF /4       | JMP r/m32    | M     | N.S.        | Valid           | Jump near, absolute indirect, address given in r/m32. Not supported in 64-bit mode. |
| FF /4       | JMP r/m64    | M     | Valid       | N.E.            | Jump near, absolute indirect, RIP = 64-Bit offset from register or memory. |
| EA cd       | JMP ptr16:16 | S     | Inv.        | Valid           | Jump far, absolute, address given in operand.                |
| EA cp       | JMP ptr16:32 | S     | Inv.        | Valid           | Jump far, absolute, address given in operand.                |
| FF /5       | JMP m16:16   | M     | Valid       | Valid           | Jump far, absolute indirect, address given in m16:16.        |
| FF /5       | JMP m16:32   | M     | Valid       | Valid           | Jump far, absolute indirect, address given in m16:32.        |
| REX.W FF /5 | JMP m16:64   | M     | Valid       | N.E.            | Jump far, absolute indirect, address given in m16:64.        |



关于指令操作表中的 Opcode Column 定义在 `******* Opcode Column in the Instruction Summary Table (Instructions without VEX Prefix)` 章节中

> https://xem.github.io/minix86/manual/intel-x86-and-64-manual-vol2/o_b5573232dd8f1481-130.html

**cb**: Code byte, 表示一个 8 位（1 字节）的值，通常是相对偏移量或立即数。

**cw**: Code word, 表示一个 16 位（2 字节）的值，通常是相对偏移量或立即数。

**cd**: Code double-word, 表示一个 32 位（4 字节）的值，通常是相对偏移量或立即数。

**cp**: Code pointer, 表示一个段:偏移（16 位段 + 16 位偏移）指针，用于特定的远跳转或调用指令。

**/4**: 斜杠后的数字表示操作码的扩展字段 (ModR/M 字节中的 reg/mem 字段)，用于指定指令变体。例如，`/4` 可能指定了某个指令使用特定的寄存器组合。

**cd/q**: 表示指令可能使用 32 位（双字）或 64 位（四字）操作数，这取决于操作数大小。







