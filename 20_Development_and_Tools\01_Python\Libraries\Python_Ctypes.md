<GeminiOptimizationFrom>Python/Ctypes.md</GeminiOptimizationFrom>
# Python `ctypes`

---



## 引用

```python
import ctypes
```



### Magic 2+2=5

---

> https://blog.csdn.net/yueguanghaidao/article/details/35644165



```python
import sys
import ctypes

# 获取整数的大小为 32
sys.getsizeof(id(4))
# 将小整数5的缓存地址移动到4的地址中
ctypes.memmove(id(4), id(5), 32)
# 计算 2+2 的值, 此时计算结果为5
print(2+2)
```





## 创建结构体

```python
import ctypes

# 创建 C 结构体
class SYSTEM_HANDLE(ctypes.Structure):
    _fields_ = [
        ("ProcessId", ctypes.c_ushort),
        ("CreatorBackTraceIndex", ctypes.c_ushort),
        ("ObjectTypeIndex", ctypes.c_byte),
        ("HandleAttributes", ctypes.c_byte),
        ("HandleValue", ctypes.c_ushort),
        ("Object", ctypes.c_void_p),
        ("AccessMask", ctypes.c_ulong),
    ]
```



从文件中读取结构体

```python
# 读取 Trace
with open(TRACE_FILE, "rb") as f:
    for i in range(5):
        # AotaInsnRecord 为一个 ctypes.Structure
        trace = AotaInsnRecord()
        f.readinto(trace)
        print(f"{trace.id}, {bytes(trace.op_valuesbefore[0])}")
```



将结构体转换为 `Bytes`

```python3
t = AotaInsnRecord()
data = bytes(t)
```





## Ctypes Windows

```python
import ctypes
from ctypes import wintypes as wt

system32 = ctypes.windll.kernel32
# https://learn.microsoft.com/en-us/windows/win32/api/processthreadsapi/nf-processthreadsapi-openprocess
OpenProcess = system32.OpenProcess
OpenProcess.argtypes = [wt.DWORD, wt.BOOL, wt.DWORD]
OpenProcess.restype = wt.HANDLE
# https://learn.microsoft.com/en-us/windows/win32/api/memoryapi/nf-memoryapi-readprocessmemory
ReadProcessMemory = system32.ReadProcessMemory
ReadProcessMemory.argtypes = [wt.HANDLE, wt.LPCVOID, wt.LPVOID, ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)]
ReadProcessMemory.restype = wt.BOOL
```



