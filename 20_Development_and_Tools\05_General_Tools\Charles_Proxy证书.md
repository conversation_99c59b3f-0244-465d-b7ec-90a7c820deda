<GeminiOptimizationFrom>Tools/Charles证书.md</GeminiOptimizationFrom>
# Charles 证书

---





> 密钥生成工具: https://www.zzzmode.com/mytools/charles/



1. 安装新证书之前, 请确保旧的证书已经被卸载. 如下图, 先重置证书再安装新的跟证书

<img src="https://qiniu.maikebuke.com/20230213102225.png" width="75%" />



2. 在 `winrun` 中运行 `Microsoft 管理控制台` , 方法为 `win+r | mmc`:

<img src="https://qiniu.maikebuke.com/20230213102524.png" style="zoom:50%;" />

3. 在 `Microsoft 管理控制台` 中, 添加证书管理单元, 方法为 `文件-添加/删除管理单元-证书`:

<img src="https://qiniu.maikebuke.com/image-20230213102944941.png" alt="image-20230213102944941" style="zoom:50%;" />



![image-20230213103334782](https://qiniu.maikebuke.com/image-20230213103334782.png)

4. 在 `受信任的根证书颁发机构` 与 `中间证书颁发机构` 中, 找到 `Charles Proxy CA` , 并删除:

![image-20230213103648276](https://qiniu.maikebuke.com/image-20230213103648276.png)

5. 安装证书:

    

<img src="https://qiniu.maikebuke.com/image-20230213103816674.png" alt="image-20230213103816674" style="zoom: 33%;" />

<img src="https://qiniu.maikebuke.com/image-20230213103900648.png" alt="image-20230213103900648" style="zoom: 33%;" />

<img src="https://qiniu.maikebuke.com/image-20230213103949137.png" alt="image-20230213103949137" style="zoom: 33%;" />