<GeminiOptimizationFrom>Python/Github API.md</GeminiOptimizationFrom>
# Python Github API

---







```python
import urllib
import urllib3
# 获取系统代理配置
proxies = urllib.request.getproxies()
# 创建代理连接池
http = urllib3.ProxyManager(proxies["http"])

token = "xxxxxxxx"
base = "https://api.github.com/"
header = {
    "Accept": "application/vnd.github+json",
    "Authorization": f"Bearer {token}",
}
# 设置连接池的 Headers
http.headers = header
# 发送请求
url = f"{base}/users"
data = json.dumps({}, ensure_ascii=False)
resp = http.request(method="get", url=url, body=data)

print(resp.data)
```



