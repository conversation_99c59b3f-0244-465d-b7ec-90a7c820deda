<GeminiOptimizationFrom>IoT/ESP8266.md</GeminiOptimizationFrom>
# ESP8266

---



> 文档: https://docs.micropython.org/en/latest/esp8266/general.html
>
> MicroPython: https://micropython.org/download/#esp8266
>
> 固件: https://micropython.org/download/esp8266-1m/



### 刷写 MicroPython 固件



```bash
# 安装串口通信工具
pip install esptool
# 清除远程flash
esptool.py --port COM3 erase_flash
# 烧录固件, 在 https://micropython.org/download/#esp8266
esptool.py --port COM3 --baud 460800 write_flash --flash_size=detect 0 esp8266-20170108-v1.8.7.bin
```





### 串口通信

> https://docs.micropython.org/en/latest/esp8266/tutorial/repl.html



在 `baud 115200` 波特率通信, 可以直接交互 `Python` 命令, 选择以 `\r\n` 结尾.



**连接 WIFI**

```python
# 导入网络包
import network

# 获取 WIFI 连接情况
sta_if = network.WLAN(network.STA_IF)
# 获取热点情况
ap_if = network.WLAN(network.AP_IF)

# 查看 WIFI 是否开启, 默认为 False
sta_if.active()
# 查看热点是否开启, 默认为 True
ap_if.active()

# 查看热点配置
ap_if.ifconfig()

# 开启 WIFI
sta_if.active(True)
# 配置 WIFI 连接
sta_if.connect('<your SSID>', '<your key>')
# 判断 WIFI 是否成功连接
sta_if.isconnected()
# 获取 WIFI 信息
sta_if.ifconfig()

# 关闭热点
ap_if.active(False)
```





**开启 WebREPL**

可以使用 `WebREPL` 运行脚本并上传文件:

```Python
# 在交互环境">>>"下, 输入以下命令初始化 WebREPL 环境
# 访问此网站使用, 不能开启 https: http://micropython.org/webrepl
import webrepl_setup
```



> 如何关闭https: edge://net-internals/#hsts





### 运行脚本

> https://docs.micropython.org/en/latest/esp8266/tutorial/filesystem.html#start-up-scripts

ESP8266 在启动时会专门处理两个文件: `boot.py` 和 `main.py`. 首先执行 `boot.py` 脚本(如果存在), 然后在完成后执行 `main.py` 脚本.













