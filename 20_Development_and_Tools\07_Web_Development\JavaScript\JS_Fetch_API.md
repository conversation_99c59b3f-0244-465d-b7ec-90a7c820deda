<GeminiOptimizationFrom>JavaScript/fetchapi.md</GeminiOptimizationFrom>
# Fetch API

---



```javascript
const TELEGRAPH_URL = 'https://aicursor.com';

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url);
  const headers_Origin = request.headers.get("Access-Control-Allow-Origin") || "*"
  url.host = TELEGRAPH_URL.replace(/^https?:\/\//, '');
  const modifiedRequest = new Request(url.toString(), {
    headers: request.headers,
    method: request.method,
    body: request.body,
    redirect: 'follow'
  });
  const response = await fetch(modifiedRequest);
  const modifiedResponse = new Response(response.body, response);
  // 添加允许跨域访问的响应头
  modifiedResponse.headers.set('Access-Control-Allow-Origin', headers_Origin);
  return modifiedResponse;
}

```



```java
const TELEGRAPH_URL = 'https://chat-pr4yueoqha-ue.a.run.app';

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
});

async function handleRequest(request) {
  // 验证请求头中的 RH-Authorization 字段, 作为接口的请求密码
  const RH_Authorization = request.headers.get("RH-Authorization");
  if ( RH_Authorization != g_auth ) {
    const err_body = JSON.stringify({code: 1, data: "没有正确的访问权限!"});
    const err_init = {
      status: 401,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      statusText: "Unauthorized",
    };
    // https://developers.cloudflare.com/workers/runtime-apis/response
    const err_resp = new Response(body=err_body, init=err_init);
    return err_resp;
  }
  // 删除 header 中的 rh 开头的字段
  const rheader = new Headers();
  request.headers.forEach((v,k) => {
    if (!k.toLowerCase().startsWith("rh-")) {
      rheader.append(k, v);
    }
  });
  // 处理请求转发
  const url = new URL(request.url);
  const headers_Origin = request.headers.get("Access-Control-Allow-Origin") || "*";
  url.host = TELEGRAPH_URL.replace(/^https?:\/\//, '');
  const modifiedRequest = new Request(url.toString(), {
    headers: rheader,
    method: request.method,
    body: request.body,
    redirect: 'follow'
  });
  const response = await fetch(modifiedRequest);
  const modifiedResponse = new Response(response.body, response);
  modifiedResponse.headers.set('Access-Control-Allow-Origin', headers_Origin);
  return modifiedResponse;
}
```



