<GeminiOptimizationFrom>AFL/输入输出文件格式.md</GeminiOptimizationFrom>
# AFL 输入输出文件格式

---



> https://sayfer.io/blog/fuzzing-part-1-the-theory
>
> https://web.archive.org/web/20230626140725/https://sayfer.io/blog/fuzzing-part-1-the-theory/

> https://sayfer.io/blog/fuzzing-part-2-fuzzing-with-afl/
>
> https://web.archive.org/web/20230626140523/https://sayfer.io/blog/fuzzing-part-2-fuzzing-with-afl/



### 1. AFL 状态显示 `show_stats`

```
                        american fuzzy lop 2.57b (gzip)

┌─ process timing ─────────────────────────────────────┬─ overall results ─────┐
│        run time : 0 days, 0 hrs, 0 min, 29 sec       │  cycles done : 1      │
│   last new path : none yet (odd, check syntax!)      │  total paths : 10     │
│ last uniq crash : none seen yet                      │ uniq crashes : 0      │
│  last uniq hang : none seen yet                      │   uniq hangs : 0      │
├─ cycle progress ────────────────────┬─ map coverage ─┴───────────────────────┤
│  now processing : 3* (30.00%)       │    map density : 0.59% / 0.59%         │
│ paths timed out : 0 (0.00%)         │ count coverage : 1.00 bits/tuple       │
├─ stage progress ────────────────────┼─ findings in depth ────────────────────┤
│  now trying : splice 1              │ favored paths : 1 (10.00%)             │
│ stage execs : 11/32 (34.38%)        │  new edges on : 1 (10.00%)             │
│ total execs : 17.2k                 │ total crashes : 0 (0 unique)           │
│  exec speed : 570.9/sec             │  total tmouts : 0 (0 unique)           │
├─ fuzzing strategy yields ───────────┴───────────────┬─ path geometry ────────┤
│   bit flips : 0/320, 0/310, 0/290                   │    levels : 1          │
│  byte flips : 0/40, 0/30, 0/10                      │   pending : 0          │
│ arithmetics : 0/2229, 0/325, 0/0                    │  pend fav : 0          │
│  known ints : 0/257, 0/835, 0/440                   │ own finds : 0          │
│  dictionary : 0/0, 0/0, 0/0                         │  imported : n/a        │
│       havoc : 0/10.5k, 0/1320                       │ stability : 100.00%    │
│        trim : 99.61%/190, 0.00%                     ├────────────────────────┘
──────────────────────────────────────────────────────┘          [cpu000:  2%]
```



### 2. 输入目录与输出目录

<img src="https://qiniu.maikebuke.com/image-20230626215628991.png" alt="image-20230626215628991" style="zoom: 50%;" />



输出目录中各个目录或文件的作用分别如下:

- `crashes` -- 存储能够造成测试样本崩溃的输入用例的目录, 文件名称包含以下内容: 崩溃的内核信号、AFL 用于创建导致崩溃的输入文件的 ID、AFL 开始运行以来经过的时间，以及用于从其种子生成输入的突变; 

- `hangs` -- 存储造成程序挂起或超时的输入用例的目录

- `queue` -- 当前时刻所有等待尝试的输入文件

- `.cur_input` -- 本质上, AFL 会将启动命令中的`@@`替换为 `.cur_input` 的绝对路径, 该文件为当前的输入文件

- `cmdline` -- 启动 AFL 使用的命令

- `fuzz_bitmap` -- bitmap 文件

- `fuzzer_stats` -- 关于 fuzzing 过程中的一些统计信息, 人类可读

- `plot_data` -- fuzzing 过程中的信息, 用于 plot 数据



































