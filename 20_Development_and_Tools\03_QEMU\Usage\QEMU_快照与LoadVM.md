<GeminiOptimizationFrom>QEMU/QEMU 快照loadvm.md</GeminiOptimizationFrom>
# QEMU Loadvm 快照系统

---



```c
// qemu/migration/savevm.c

bool save_snapshot(const char *name, bool overwrite, const char *vmstate,
                  bool has_devices, strList *devices, Error **errp);

bool load_snapshot(const char *name, const char *vmstate,
                   bool has_devices, strList *devices, Error **errp);
```



