# 系统配置

本文档整理了 Linux 系统配置相关的命令和技巧。

## 安全配置

### 关闭 ASLR

```shell
echo 'kernel.randomize_va_space = 0' >> /etc/sysctl.conf
reboot
# 检查是否开启了 ASLR, 如果是 0 则为关闭
cat /proc/sys/kernel/randomize_va_space
```

## 系统启动配置

### 自动挂载配置

编辑 `/etc/fstab` 文件可以配置系统启动时自动挂载的文件系统。

### 卸载挂载的软盘

如果开机出现 `blk_update_request: I/O error, dev df0, sector 0` 类似的提示，是由于 `/dev/fd0` 挂载了不存在的软盘导致的，可以编辑 `/etc/fstab` 文件，将其中的加载软盘的代码注释。

## 相关链接

- [[磁盘和文件系统]]
- [[网络和SSH]]
- [[用户和权限]]
- [[进程和系统监控]]
