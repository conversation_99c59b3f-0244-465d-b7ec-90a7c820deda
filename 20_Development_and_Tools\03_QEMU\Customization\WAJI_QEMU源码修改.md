<GeminiOptimizationFrom>WAJI/QEMU Edit.md</GeminiOptimizationFrom>
# WAJI
----

**构建过程如下**
pip install tomli   # version < 3.11
pip install tomllib # version >= 3.11
```shell
./configure --enable-debug --enable-waji --enable-plugins
./configure --target-list="i386-softmmu,x86_64-softmmu,i386-linux-user, x86_64-linux-user,arm-softmmu,arm-linux-user,riscv32-softmmu,riscv64-softmmu,riscv32-linux-user,riscv64-linux-user" --python=`which python3` --enable-debug --enable-waji --enable-plugins --enable-slirp --enable-debug
make -j
```


## About Code
### 1.1 修改编译脚本, 使编译时能够包含 WAJI 相关选项
#### 1.1.1 configure 配置
位于 `configure` 文件中

**编译 Option 配置**

在遍历 `opt` 选项时, 添加 WAJI 相关选项. 搜索 `--disable-plugins` 字符串, 然后在下方添加 `--enable-waji` 和 `--disable-waji` 两个选项, 并分别配置 `waji` 变量. 

同时, 在 `$show_help` 命令中修改帮助文档. 在高版本(V9.1.0)中, 与 Plugin 有关的选项放在了 `meson_options_help` 脚本中, 修改提示信息需要在 `scripts/meson-buildoptions.py` 以及 `scripts/meson-buildoptions.sh` 中对应位置进行修改. 

**链接选项配置**
搜索 `LINKS="Makefile"` 关键字, 需要链接 WAJI Plugin 的相关目录, 例如:
```shell
LINKS="$LINKS contrib/waji-plugins/Makefile "
```

**配置全局编译宏**
在生成 `config-host.mak` 的过程中, 需要将编译的必要参数写入 `<build>/config-host.mak` 目录中, 包括编译时的必要宏. 在 `configure` 中搜索 `# generate config-host.mak`, 在恰当的位置添加全局 WAJI 宏配置代码. 

WAJI 相关代码需要在 `--enable-waji` 时开启, 因此需要使用相关宏 `CONFIG_WAJI` 进行包裹. 具体如下:
```shell
if test "$waji" = "yes" ; then
    echo "CONFIG_WAJI=y" >> $config_host_mak
fi
```


在上述配置完成后, 需要确认 `<build>/config-host.mak` 中是否有 `CONFIG_WAJI` 宏定义.


**QEMU 选项生成**
`qemu-options.hx`

搜索 `-plugin` 关键字, 在其后添加 WAJI 相关的说明




### 1.2 头文件相关(-I 编译选项)

- `include/waji/waji-plugin.h` WAJI 核心声明, 包括:
    - 内存读写函数(cpu_memory_rw_debug)
    - CPU 信息修改函数(regs, xmm, flags)
    - 关键结构体
    - 关键宏
- `include/waji/waji-arch-x86_64.h` 在其中添加相关头文件'
- `meson.build` 中, 找到 `CONFIG_PLUGIN` 参数关于 `install_headers` 的调用(`install_headers('include/qemu/qemu-plugin.h')`), 并在其后添加 `CONFIG_WAJI` 相关的配置; 并且在 `summary_info` 中添加相关的说明;



### 1.3 关键插桩点
- `accel/tcg/cpu-exec.c` 可以在 `cpu_exec` 函数中插桩, 获取 CPU 执行的记录;
- `target/i386/helper.c` 中的 `cpu_x86_update_cr3()` 函数是 CR3 寄存器的更新点;



### 1.4 内存读写函数
`cpu.c` 文件中的 `cpu_memory_rw_debug` 函数

如果在此处添加了新的函数, 需要在 `include/exec/cpu-common.h` 中添加相应的函数说明. 


`softmmu/physmem.c` 中可以添加物理内存的读写接口. 


`softmmu/vl.c` 可以用于初始化 QEMU 虚拟化硬件有关的一些信息, 例如 `MachineState` 等; 同时, 可以在 `qemu_init()` 中的 `QEMU_OPTION_plugin` 附近, 配置 WAJI 有关的初始化操作. 

### 1.5 用户态插桩
#### 1.5.1 初始化全局 WAJI 对象
`linux-user/main.c` 

1. 在 include 中添加 WAJI 相关头文件
2. 在 `main()` 函数中的恰当位置, 初始化(malloc)全局的 WAJI 对象

#### 1.5.2 ELF 加载过程
`linux-user/elfload.c`

1. 头部添加 WAJI 的 User-Mode 对象;
2. 获取 ELF 加载信息: `load_elf_image()` 函数中, 在 `mmap_unlock();` 之后, 通过 `image_name`, `info->load_addr`, `info->start_code` 以及 `info->end_code`;
3. 获取 lib 加载信息: 在 `linux-user/syscall.c` 中, 找到 `do_syscall1()` 函数中的 `TARGET_NR_mmap` 系统调用. 该系统调用在正常的代码中也会调用, 其中, 有两个特征能够帮助确定该调用是否为库映射: `offset(arg6)==0 && fd(arg5)>=2`


### 1.6 插件加载位置以及插桩
`plugins/loader.c` 中, 包括插件信息的初始化, CPU REG 状态读写以及 plugin 超参数读取. 

### 1.7 修改 CPUID 的模拟参数
`target/i386/cpu.c` 中的 `cpu_x86_cpuid()` 函数中,  `case 0x40000000` 处可以将 TCG 模拟 flag 调整为 KVM 模拟 flag. 

```c
// WAJI Mark:
// Always disguise the device in KVM mode
// https://kernel.org/doc/html/latest/virt/kvm/x86/cpuid.html
// *eax = 0x40000001;
// *ebx = 0x4b4d564b;      /* KVMK */
// *ecx = 0x564b4d56       /* VMKV */
// *edx = 0x0000004d;      /* M    */
memcpy(signature, "KVMKVMKVM", 9);
*eax = 0x40000001;
*ebx = signature[0];
*ecx = signature[1];
*edx = signature[2];
// WAJI End;
```

### 1.8 系统调用插桩点

`target/i386/tcg/translate.c` 中的 `disas_insn()` 函数, 其中:
- `case 0xcd` 表示 `int N` 指令, 可以在此处获取 `int 0x80` 事件; 
- `case 0x105` 表示 `syscall` 指令, 可以在此处获取系统调用事件. 


### 1.9 修改模拟器时间流速



`QEMU` 虚拟机中的所有时间都是通过 `int64_t qemu_clock_get_ns(QEMUClockType type)` 函数实现的, 包含三种类型的时间:

- QEMU_CLOCK_REALTIME
- QEMU_CLOCK_VIRTUAL
- QEMU_CLOCK_HOST
- QEMU_CLOCK_VIRTUAL_RT

时间缩放, 原理为, 在虚拟机 "启动" 时保存虚拟机的时间作为 `base_time`, 然后后续在获取时间时, 通过当前时间 `clock` 与 `base_time` 的差值 , 获取时间差 `delta`, 然后通过例如 `base_time + delta / 2` 的方式返回当前经过 "缩放" 后的时间

我的实现方式如下:

```c
// 全局变量
struct QEMUTimeScaline {
    int64_t realtime;
    int64_t virtual;
    int64_t host;
    int64_t virtual_rt;
} base_time;

int64_t qemu_clock_get_ns(QEMUClockType type)
{
    int64_t clock, base, delta;
    switch (type) {
    case QEMU_CLOCK_REALTIME:
        clock = get_clock();
        if (unlikely(clock < base_time.realtime || !base_time.realtime)) {
            base_time.realtime = clock;
        }
        base = base_time.realtime;
        break;
    default:
    case QEMU_CLOCK_VIRTUAL:
        clock = cpus_get_virtual_clock();
        if (unlikely(clock < base_time.virtual || !base_time.virtual)) {
            base_time.virtual = clock;
        }
        base = base_time.virtual;
        break;
    case QEMU_CLOCK_HOST:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_HOST, get_clock_realtime());
        if (unlikely(clock < base_time.host || !base_time.host)) {
            base_time.host = clock;
        }
        base = base_time.host;
        break;
    case QEMU_CLOCK_VIRTUAL_RT:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_VIRTUAL_RT, cpu_get_clock());
        if (unlikely(clock < base_time.virtual_rt || !base_time.virtual_rt)) {
            base_time.virtual_rt = clock;
        }
        base = base_time.virtual_rt;
        break;
    }
    delta = clock - base;
    return base + (delta >> 3);
}
```

或者更简洁的:

```c
int64_t base_time[4];

int64_t qemu_clock_get_ns(QEMUClockType type)
{
    int64_t clock, base, delta;
    switch (type) {
    case QEMU_CLOCK_REALTIME:
        clock = get_clock();
        break;
    default:
    case QEMU_CLOCK_VIRTUAL:
        clock = cpus_get_virtual_clock();
        break;
    case QEMU_CLOCK_HOST:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_HOST, get_clock_realtime());
        break;
    case QEMU_CLOCK_VIRTUAL_RT:
        clock = REPLAY_CLOCK(REPLAY_CLOCK_VIRTUAL_RT, cpu_get_clock());
        break;
    }
    if ( unlikely(clock < base_time[type] || !base_time[type]) ) {
        base_time[type] = clock;
    }
    base = base_time[type];
    delta = clock - base;
    return base + (delta >> 3);
}
```

值得注意的是, 该实现有 bug, 不能应对重复加载快照的情况. 









