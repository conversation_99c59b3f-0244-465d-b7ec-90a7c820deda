<GeminiOptimizationFrom>LLVM/Clang/01. Clang基本组件.md</GeminiOptimizationFrom>
# Clang 基本组件

---



`Clang` 包含 `LibClang`, `Clang Plugins`以及`LibTooling`三个组件

> https://clang.llvm.org/doxygen/group__CINDEX.html
>
> https://clang.llvm.org/docs/ClangPlugins.html
>
> https://clang.llvm.org/docs/LibTooling.html



### LibClang

只包含了 Clang 中的一小部分 API, 使得用户能够从源码中解析 AST, 也能够有奖物理原位置与 AST 元素关联的能力. 具体而言, `LibClang` 暴露了一些稳定的 C 语言接口, 能够兼容多版本的 Clang 编译器, 以及为多语言交互场景提供了支持.

**优点**

- 可以使用 C++ 之外的编程语言与 Clang 进行交互(如 [Python](https://github.com/llvm/llvm-project/tree/main/clang/bindings/python))
- 希望使用稳定的交互接口, 以满足前后兼容性
- 不需要了解 AST 的细节, 使用一个高度抽象的接口扫描 AST

**缺点**

- 不能够完全的控制 Clang AST

> https://pypi.org/project/clang/
>
> https://github.com/llvm/llvm-project/tree/main/clang/bindings/python
>
> https://github.com/trolldbois/python-clang/



### Clang Plugins

`Clang Plugins` 是一个动态库, 允许在编译过程中对 AST 执行复杂的操作, 并且由于在编译时加载, 能够轻松的集成到真实的编译环境中. `Clang Plugins` 可以完成以下需求:

1. 为项目添加特殊的 `lint-style` 警告(或 Error)
2. 为编译过程创建一个额外的 build artifacts(组件)

以下场景**可以**使用:

- 需要在任何依赖改变时重新运行此工具
- 希望此工具能够构建或破坏一个构建过程
- 希望完全控制 Clang AST

以下场景**不适用**:

- 希望独立于构建环境运行此工具
- 希望**完全控制** Clang 的设置, 包括内存虚拟文件的映射过程
- 需要在项目中的特定子集文件中运行此工具(, 因为这些文件不一定会触发 rebuild)



### LibTooling

`LibTooling` 是一个用于实现独立工具, 以及作为 Clang 工具套件服务中的 C++ 开发接口, 能够用于:

1. 简单的语法检查
2. 重构工具

**优点**

- 独立于编译系统, 能够在单个文件或文件子集中运行工具
- 能够**完全控制**Clang AST
- 希望与 `Clang Plugins` 共享代码

**以下场景不适用**

- 希望当依赖文件改变时出发的部分编译时
- 希望拥有一个稳定的 API 时(LibTooling 在不同版本之间的 API 变化可能非常大)
- 希望能够使用高级抽象接口时(如 Python Clang 中提供的 Cursor 功能或 Code Completion 时)
- 不希望使用 C++ 开发时





------



> 使用 Clang Plugin 开发一个`Unix Stream API Checker`: https://llvm.org/devmtg/2012-11/Zaks-Rose-Checker24Hours.pdf





