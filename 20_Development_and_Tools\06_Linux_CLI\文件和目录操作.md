# 文件和目录操作

本文档整理了 Linux 系统中常用的文件和目录操作命令。

## 基本文件操作

### 查看文件二进制内容

```shell
xxd <file>.bin
```

## 压缩与解压缩

### DEB 包操作

```shell
# 安装一个 dpkg 包
sudo dpkg -i <package>.deb
# 导出数据信息
dpkg -x <package>.deb <path>/
# 导出控制信息
dpkg -e <package>.deb <path>/
# 解压当前目录下所有的 deb 包
for file in *.deb; do dpkg-deb -x $file ${file%%.deb}; done
```

### TAR 包操作

```bash
# 压缩目录
tar -zcvf <dest>.tar.gz <src>/
# 解压目录
tar -zxvf <archive>.tar.gz

# 压缩相对目录
tar -czf <archive>.tar.gz -C ${TARGET_DIR} ${TARGET_NAME}
```

#### TAR 命令参数说明

| 参数 | 含义                             | 是否必选     |
| ---- | -------------------------------- | ------------ |
| -f   | 指定档案名字, 必须是最后一个参数 | 必选         |
| -c   | 建立压缩档案                     | cxtru 五选一 |
| -x   | 解压                             | cxtru 五选一 |
| -t   | 查看文件内容                     | cxtru 五选一 |
| -z   | 有gzip属性的                     |              |
| -j   | 有bz2属性的                      |              |
| -v   | 控制台中显示过程                 |              |
| -O   | 将文件解开到标准输出             |              |

### 并行压缩

```bash
# 并行压缩
tar --use-compress-program="pigz -k -p100" -cvf archive.tgz source/*
# 并行解压
tar --use-compress-program="pigz -k -p8" -xvf archive.tgz
```

## 文件搜索

### 按文件名搜索

使用 `find` 命令搜索文件名：

| 选项        | 含义                                   |
| ----------- | -------------------------------------- |
| find <PATH> | 搜索的起始目录                         |
| -type       | 搜索的类型, f:文件, d:目录, l:符号链接 |
| -name       | 搜索的pattern                          |

```bash
find . -name "foo*"
find ./ -name "pattern"
find . -type f -name "*.conf"
find . -print | grep -i foo
find / -type f -exec grep -sH 'text-to-find-here' {} \; 2>/dev/null
```

### 按文件内容搜索

根据 `pattern` 搜索文件内容，使用 `grep` 命令：

参考：[man grep](https://ss64.com/bash/grep.html) | [Stackoverflow](https://stackoverflow.com/questions/16956810/how-to-find-all-files-containing-specific-text-string-on-linux)

| 选项          | 含义                   |
| ------------- | ---------------------- |
| -r, -R        | 递归搜索               |
| -n            | 显示行号               |
| -w            | 匹配整个单词           |
| -l            | 只给出匹配文件的文件名 |
| -e            | 搜索过程中使用的模式   |
| --exclude     | 添加排除项             |
| --exclude-dir | 添加排除目录           |
| --include     | 只搜索include的项目    |

```bash
# 基本搜索
grep -rnw '/path/to/somewhere/' -e 'pattern'
# 只搜索.c和.h文件
grep --include=\*.{c,h} -rnw '/path/to/somewhere/' -e "pattern"
# 排除.o文件
grep --exclude=\*.o -rnw '/path/to/somewhere/' -e "pattern"
# 排除目录
grep --exclude-dir={dir1,dir2,*.dst} -rnw '/path/to/search/' -e "pattern"
```

## 特殊文件操作

### 删除无法通过 shell 输入文件名的文件

**使用 inode 号码删除**

每个文件在Linux中都有一个唯一的inode号码（索引节点, i节点），它可以用于标识文件。如果无法根据文件名删除文件，可以使用文件的inode号码。

```bash
# 查看文件的 inode 号码
$ ls -li
# 31472500 -rw-rw-r-- 1 <USER> <GROUP>    0 May  27 03:32 ''$'\001\240''7@؁'$'\001''@8'
# 第一列的数字就是文件的 inode 号码, 通过以下命令查看文件然后删除
$ find . -inum [inode number]
$ find . -inum [inode number] -delete
```

## 文件拷贝

### 带进度的文件拷贝

```shell
rsync -ah --progress <source> <dest>
```

## 相关链接

- [[文本处理]]
- [[进程和系统监控]]
- [[磁盘和文件系统]]
