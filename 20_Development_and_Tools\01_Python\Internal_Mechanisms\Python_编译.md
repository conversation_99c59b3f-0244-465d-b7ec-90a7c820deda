<GeminiOptimizationFrom>Python/编译.md</GeminiOptimizationFrom>
# Python 编译

---



```shell
git clone https://github.com/python/cpython.git
cd cpython
git checkout v3.10.13
sudo apt-get install build-essential gdb lcov pkg-config libbz2-dev libffi-dev libgdbm-dev libgdbm-compat-dev liblzma-dev ibncurses5-dev libreadline6-dev libsqlite3-dev libssl-dev lzma lzma-dev tk-dev uuid-dev zlib1g-dev
./configure --prefix=${HOME}/.local/python/3.10.13 --enable-shared --enable-optimizations --disable-ipv6 --with-static-libpython LDFLAGS="-Wl,--rpath=${HOME}/.local/python/3.10.13/lib"
make -j
make install
```



为了应对开启 `--enable-shared` 选项后无法找到动态库的情况, 可以在编译时开启 `LDFLAGS` 设置 `rpath`

```bash
export LDFLAGS="-Wl,-rpath,${HOME}/.local/python/3.9.16/lib"
./configure --enable-optimizations --prefix=${HOME}/.local/python/3.9.16 --enable-shared
```



