<GeminiOptimizationFrom>Certificate/acme.md</GeminiOptimizationFrom>
# acme.sh

---



```shell
export DP_Id="xxxxxx"
export DP_Key="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

ACME_SH=${HOME}/.acme.sh/acme.sh

${ACME_SH} --set-default-ca --server letsencrypt

${ACME_SH} --register-account -m <EMAIL>

${ACME_SH} \
        --issue \
        --dns dns_dp \
        -d maikebuke.com \
        -d *.maikebuke.com \
        --days 90 \
        --keylength 2048 \
        --dnssleep 30 \
        --home /home/<USER>/cert \
        -m <EMAIL>
        # --key-file /home/<USER>/cert/maikebuke.com.key \
        # --fullchain-file /home/<USER>/cert/maikebuke.com.cer

```



```yaml
version: "3"
services:
  acme.sh:
    image: neilpang/acme.sh
    container_name: acme.sh
    restart: always
    command: daemon
    environment:
      - DP_Id="xxxxxx"
      - export DP_Key="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    volumes:
      - /etc/nginx/acme/:/acme.sh
    network_mode: host

```



