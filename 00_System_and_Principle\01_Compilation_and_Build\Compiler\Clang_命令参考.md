# Clang 命令参考

本文档整理了 Clang 编译器的常用命令和选项。

## AST 操作

### 导出 AST（抽象语法树）

使用 Clang 导出源代码的抽象语法树：

```shell
clang -Xclang -ast-dump -fsyntax-only <source_file>
```

**参数说明**：
- `-Xclang`：将后续参数传递给 Clang 前端
- `-ast-dump`：导出 AST
- `-fsyntax-only`：只进行语法检查，不生成目标文件

## 预处理操作

### 查看头文件引用

查看编译过程中包含的头文件：

```shell
echo "#include <unistd.h>" | gcc -E -H -o /dev/null -
```

**参数说明**：
- `-E`：只进行预处理
- `-H`：显示包含的头文件
- `-o /dev/null`：不输出预处理结果
- `-`：从标准输入读取

## 相关链接

- [[GCC_命令参考]]
- [[编译工具链]]
- [[Makefile_指南]]
